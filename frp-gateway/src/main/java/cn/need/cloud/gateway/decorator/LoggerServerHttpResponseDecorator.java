package cn.need.cloud.gateway.decorator;

import cn.need.cloud.gateway.util.DataBufferFix;
import cn.need.cloud.gateway.util.DataBufferWrapper;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.lang.NonNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static cn.need.framework.common.core.lang.ObjectUtil.isNull;

/**
 * gateway response 装饰对象
 *
 * <AUTHOR>
 */
@Slf4j
public class LoggerServerHttpResponseDecorator extends ServerHttpResponseDecorator {

    /**
     * 装饰对象中缓存wrapper对象
     */
    private DataBufferWrapper wrapper = null;

    public LoggerServerHttpResponseDecorator(ServerHttpResponse delegate) {
        super(delegate);
    }

    /**
     * 重写父类的写数据方法
     *
     * @param body 响应body
     * @return Mono<Void>
     */
    @NonNull
    @Override
    public Mono<Void> writeWith(@NonNull Publisher<? extends DataBuffer> body) {
        return DataBufferFix.join(Flux.from(body)).doOnNext(d -> wrapper = d).flatMap(d -> super.writeWith(wrapperBuffer()));
    }

    /**
     * 重写父类的写数据方法
     *
     * @param body 响应body
     * @return Mono<Void>
     */
    @NonNull
    @Override
    public Mono<Void> writeAndFlushWith(@NonNull Publisher<? extends Publisher<? extends DataBuffer>> body) {
        return writeWith(Flux.from(body).flatMapSequential(p -> p));
    }

    /**
     * 包装DataBuffer
     *
     * @return Flux<DataBuffer>
     */
    public Flux<DataBuffer> wrapperBuffer() {
        //如果wrapper为null，打印错误日志，方便调试
        if (isNull(wrapper)) {
            log.error("the DataBuffer wrapper object is null.");
            return Flux.empty();
        }
        //构造一个新的DataBuffer对象
        DataBuffer buffer = wrapper.newDataBuffer();
        if (isNull(buffer)) {
            log.error("the DataBuffer is null.");
            return Flux.empty();
        }
        //包装响应数据
        return Flux.just(buffer);
    }
}
