package cn.need.cloud.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

import java.util.UUID;

/**
 * 网关TraceId过滤器 - 支持响应式编程环境的链路追踪
 * 
 * 在响应式编程环境中，MDC无法直接使用，需要通过Reactor Context传递TraceId
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class GatewayTraceLogFilter implements GlobalFilter, Ordered {
    
    private static final String HEADER_TRACE_ID = "Trace-Id";
    private static final String TRACE_ID = "traceId";
    private static final String SPAN_SEPARATOR = ".";
    private static final String GATEWAY_PREFIX = "GW";

    @Override
    public int getOrder() {
        // 设置为最高优先级，确保TraceId在所有其他过滤器之前设置
        return Ordered.HIGHEST_PRECEDENCE + 10;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String traceId = generateOrInheritTraceId(request);
        
        // 将TraceId添加到请求头，传递给下游服务
        ServerHttpRequest mutatedRequest = request.mutate()
                .header(HEADER_TRACE_ID, traceId)
                .build();
        
        ServerWebExchange mutatedExchange = exchange.mutate()
                .request(mutatedRequest)
                .build();
        
        // 在Reactor Context中设置TraceId，供日志使用
        return chain.filter(mutatedExchange)
                .contextWrite(Context.of(TRACE_ID, traceId))
                .doOnEach(signal -> {
                    // 在每个信号处理时设置MDC，用于日志输出
                    if (signal.hasValue() || signal.hasError()) {
                        try {
                            MDC.put(TRACE_ID, traceId);
                        } catch (Exception e) {
                            // 忽略MDC设置异常，不影响主流程
                        }
                    }
                })
                .doFinally(signalType -> {
                    // 清理MDC
                    try {
                        MDC.remove(TRACE_ID);
                    } catch (Exception e) {
                        // 忽略MDC清理异常
                    }
                });
    }

    /**
     * 生成或继承TraceId，支持调用链路层级关系
     */
    private String generateOrInheritTraceId(ServerHttpRequest request) {
        String headerTraceId = request.getHeaders().getFirst(HEADER_TRACE_ID);
        
        if (StringUtils.hasText(headerTraceId)) {
            // 如果是从上游传递过来的TraceId，添加网关层级
            return headerTraceId + SPAN_SEPARATOR + GATEWAY_PREFIX + "-" + generateSpanId();
        } else {
            // 如果是新请求，生成根TraceId（网关作为入口）
            return generateRootTraceId();
        }
    }

    /**
     * 生成根TraceId（网关入口）
     */
    private String generateRootTraceId() {
        return "ROOT-" + GATEWAY_PREFIX + "-" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 生成SpanId（网关层级）
     */
    private String generateSpanId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
}
