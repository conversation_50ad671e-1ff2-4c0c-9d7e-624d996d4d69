package cn.need.framework.starter.web.log;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Enumeration;

/**
 * 日志拦截器
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@AllArgsConstructor
@Slf4j
public class RequestLoggingFilter extends CommonsRequestLoggingFilter {

    private static final String REQUEST_START_TIME = "REQUEST_START_TIME";
    private final WebLogProperties logProperties;

    @NonNull
    @Override
    protected String createMessage(@NonNull HttpServletRequest request, @NonNull String prefix, @NonNull String suffix) {
        if (!logProperties.isEnable()) {
            return "";
        }
        StringBuilder msg = new StringBuilder();
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtils.hasText(traceId) ? " [TraceId: " + traceId + "]" : "";
        msg.append("\n").append("<================================  执行请求   ================================>").append(traceInfo);
        msg.append("\n").append(request.getMethod()).append(' ');
        msg.append(request.getRequestURI());

        if (isIncludeQueryString()) {
            String queryString = request.getQueryString();
            if (queryString != null) {
                msg.append('?').append(queryString);
            }
        }

        if (isIncludeClientInfo()) {
            String client = request.getRemoteAddr();
            if (StringUtils.hasLength(client)) {
                msg.append("\n").append("客户端: ").append(client);
            }
            HttpSession session = request.getSession(false);
            if (session != null) {
                msg.append("\n").append("Session: ").append(session.getId());
            }
            String user = request.getRemoteUser();
            if (user != null) {
                msg.append("\n").append("User: ").append(user);
            }
        }

        if (isIncludeHeaders()) {
            HttpHeaders headers = new ServletServerHttpRequest(request).getHeaders();
            if (getHeaderPredicate() != null) {
                Enumeration<String> names = request.getHeaderNames();
                while (names.hasMoreElements()) {
                    String header = names.nextElement();
                    if (!getHeaderPredicate().test(header)) {
                        headers.set(header, "masked");
                    }
                }
            }
            msg.append("\n").append("Headers: ").append(headers);
        }

        if (isIncludePayload()) {
            String payload = getMessagePayload(request);
            if (payload != null) {
                msg.append("\n").append("Body: ").append(payload);
            }
        }
        if (isIncludePayload()) {
            if (request instanceof RepeatableRequestWrapper wrapper) {
                String result = wrapper.getBody() == null ? "" : new String(wrapper.getBody());
                msg.append("\n").append("Body: ").append(result);
            }
        }
        msg.append("\n").append("<================================  执行请求   ================================>").append(traceInfo);
        return msg.toString();
    }

    @Override
    protected void beforeRequest(@NonNull HttpServletRequest request, @NonNull String message) {
        // 记录请求开始时间
        request.setAttribute(REQUEST_START_TIME, System.currentTimeMillis());

        int truncationLength = logProperties.getTruncationLength();
        if (truncationLength > 0 && message.length() > truncationLength) {
            message = message.substring(0, truncationLength) + "...";
        }
        log.info(message);
    }

    @Override
    protected void afterRequest(@NonNull HttpServletRequest request, @NonNull String message) {
        // 计算请求耗时
        Long startTime = (Long) request.getAttribute(REQUEST_START_TIME);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            String traceId = MDC.get("traceId");
            String traceInfo = StringUtils.hasText(traceId) ? " [TraceId: " + traceId + "]" : "";

            StringBuilder msg = new StringBuilder();
            msg.append("\n").append("<================================  请求完成   ================================>").append(traceInfo);
            msg.append("\n").append("请求耗时: ").append(duration).append("ms");
            msg.append("\n").append("请求URL: ").append(request.getMethod()).append(" ").append(request.getRequestURI());
            msg.append("\n").append("<================================  请求完成   ================================>").append(traceInfo);

            log.info(msg.toString());
        }
    }

    @Override
    protected boolean isIncludeQueryString() {
        return logProperties.isIncludeQueryString();
    }

    @Override
    protected boolean isIncludeClientInfo() {
        return logProperties.isIncludeClientInfo();
    }

    @Override
    protected boolean isIncludeHeaders() {
        return logProperties.isIncludeHeaders();
    }

    @Override
    protected boolean isIncludePayload() {
        return logProperties.isIncludePayload();
    }

    @Override
    protected boolean shouldLog(@NonNull HttpServletRequest request) {
        if (!logProperties.isEnable() || !log.isDebugEnabled()) {
            return false;
        }
        
        // 检查请求路径是否在忽略列表中
        String requestUri = request.getRequestURI();
        if (logProperties.getIgnoredPaths() != null && !logProperties.getIgnoredPaths().isEmpty()) {
            for (String ignoredPath : logProperties.getIgnoredPaths()) {
                if (requestUri.equals(ignoredPath)) {
                    return false;
                }
            }
        }
        return true;
    }
}
