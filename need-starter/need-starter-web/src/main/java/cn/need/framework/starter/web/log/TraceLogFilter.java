package cn.need.framework.starter.web.log;

import org.slf4j.MDC;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * TraceId 设置 - 支持链路追踪层级关系
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
public class TraceLogFilter extends OncePerRequestFilter {
    private static final String HEADER_TRANCE_ID = "Trace-Id";
    private static final String TRACE_ID = "traceId";
    private static final String SPAN_SEPARATOR = ".";

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException, IOException {
        String traceId = generateOrInheritTraceId(request);
        MDC.put(TRACE_ID, traceId);
        try {
            filterChain.doFilter(request, response);
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    /**
     * 生成或继承TraceId，支持调用链路层级关系
     */
    private String generateOrInheritTraceId(HttpServletRequest request) {
        String headerTraceId = request.getHeader(HEADER_TRANCE_ID);

        if (StringUtils.hasText(headerTraceId)) {
            // 如果是从上游服务传递过来的TraceId，添加当前服务的span
            return headerTraceId + SPAN_SEPARATOR + generateSpanId();
        } else {
            // 如果是新请求，生成根TraceId
            return generateRootTraceId();
        }
    }

    /**
     * 生成根TraceId（请求入口）
     */
    private String generateRootTraceId() {
        return "ROOT-" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 生成SpanId（服务调用层级）
     */
    private String generateSpanId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
}
