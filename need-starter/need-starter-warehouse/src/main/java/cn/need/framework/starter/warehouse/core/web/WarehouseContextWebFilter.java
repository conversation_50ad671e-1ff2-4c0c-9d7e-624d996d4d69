package cn.need.framework.starter.warehouse.core.web;

import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import cn.need.framework.starter.warehouse.util.WebFrameworkUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 多仓库 Context Web 过滤器
 * 将请求 Header 中的 warehouse-id 解析出来，添加到 {@link WarehouseContextHolder} 中，这样后续的 DB 等操作，可以获得到仓库编号。
 *
 * <AUTHOR>
 */
public class WarehouseContextWebFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain chain)
            throws ServletException, IOException {
        // 设置
        Long warehouseId = WebFrameworkUtils.getWarehouseId(request);
        if (warehouseId != null) {
            WarehouseContextHolder.setWarehouseId(warehouseId);
        }

        try {
            chain.doFilter(request, response);
        } finally {
            // 清理
            WarehouseContextHolder.clear();
        }
    }

}
