# 增强版链路追踪实现指南

## 概述

本文档描述了项目中增强版链路追踪功能的实现，包括层级TraceId、统一日志格式、以及便捷的使用工具。

## 功能特性

### 1. 层级TraceId支持
- **根TraceId**：请求入口生成，格式：`ROOT-{SERVICE}-{16位ID}`
- **子TraceId**：服务调用时生成，格式：`{父TraceId}.{SERVICE}-{8位ID}`
- **调用链路**：通过`.`分隔符体现服务调用层级关系

### 2. 全模块统一日志格式
- 所有模块的logback配置都包含TraceId
- 控制台和文件日志都显示TraceId
- 无TraceId时显示`NO_TRACE`

### 3. 网关响应式支持
- 专门的`GatewayTraceLogFilter`处理响应式环境
- 通过Reactor Context传递TraceId
- 网关日志包含完整链路信息

## 技术实现

### 1. TraceId生成规则

#### 根TraceId（请求入口）
```
ROOT-{SERVICE_PREFIX}-{16位随机ID}
```
示例：
- 网关入口：`ROOT-GW-a1b2c3d4e5f67890`
- API中心入口：`ROOT-API-f1e2d3c4b5a69870`

#### 子TraceId（服务调用）
```
{父TraceId}.{SERVICE_PREFIX}-{8位随机ID}
```
示例：
- 网关→API中心：`ROOT-GW-a1b2c3d4e5f67890.API-12345678`
- API中心→业务服务：`ROOT-GW-a1b2c3d4e5f67890.API-12345678.BIZ-87654321`

### 2. 核心组件

#### TraceLogFilter（传统Web环境）
```java
// 自动生成或继承TraceId
// 支持层级关系
// 存储到MDC中
```

#### GatewayTraceLogFilter（响应式环境）
```java
// 网关专用TraceId处理
// Reactor Context传递
// 请求头注入
```

#### TraceContext工具类
```java
// TraceId管理
// 层级关系处理
// 便捷操作方法
```

#### TraceLogger工具类
```java
// 增强日志输出
// 自动包含TraceId
// 统一日志格式
```

## 使用指南

### 1. 基础使用

#### 获取TraceId
```java
String traceId = TraceContext.getTraceId();
```

#### 设置TraceId
```java
TraceContext.setTraceId("your-trace-id");
```

#### 生成子TraceId
```java
String childTraceId = TraceContext.generateChildTraceId("SERVICE");
```

### 2. 日志输出

#### 使用TraceLogger
```java
private static final TraceLogger.TraceLoggerWrapper log = TraceLogger.getLogger(YourClass.class);

// 自动包含TraceId的日志
log.info("处理业务逻辑开始，参数：{}", param);
log.error("处理失败", exception);
```

#### 使用传统Logger
```java
private static final Logger log = LoggerFactory.getLogger(YourClass.class);

// 手动添加TraceId信息
log.info("{} 处理业务逻辑开始，参数：{}", TraceContext.getTraceInfo(), param);
```

### 3. 异步处理

#### 传递TraceId到异步线程
```java
String traceId = TraceContext.getTraceId();
CompletableFuture.runAsync(() -> {
    TraceContext.runWithTraceId(traceId, () -> {
        // 异步业务逻辑
        log.info("异步处理完成");
    });
});
```

#### 带返回值的异步处理
```java
String traceId = TraceContext.getTraceId();
CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
    return TraceContext.callWithTraceId(traceId, () -> {
        // 异步业务逻辑
        return "处理结果";
    });
});
```

## 配置说明

### 1. 日志级别配置

```yaml
logging:
  level:
    cn.need.framework.starter.web.log.RequestLoggingFilter: DEBUG
    cn.need.framework.common.mybatis.log.MyBatisParseLoggingImpl: DEBUG
    cn.need.cloud.gateway.filter.GatewayTraceLogFilter: DEBUG
```

### 2. 请求日志配置

```yaml
servlet:
  request:
    log:
      enable: true
      include-query-string: true
      include-headers: false
      include-payload: true
      include-client-info: false
      truncation-length: -1
```

## 日志格式示例

### 1. 控制台日志
```
2025-07-03 10:30:15.123 +08:00 frp-gateway INFO [12345] [reactor-http-nio-2] [ROOT-GW-a1b2c3d4e5f67890] [cn.need.cloud.gateway.filter.AccessLogFilter] [AccessLogFilter.java:75] : Gateway Access Log - TraceId: ROOT-GW-a1b2c3d4e5f67890, Method: POST, Path: /api/v1/orders
```

### 2. 文件日志
```
2025-07-03 10:30:15.123 +08:00 frp-business INFO [12345] [http-nio-10030-exec-1] [ROOT-GW-a1b2c3d4e5f67890.API-12345678.BIZ-87654321] [cn.need.cloud.biz.service.OrderService] [OrderServiceImpl.java:45] : 开始处理订单创建，订单号：ORD202507030001
```

## 调用链路示例

### 完整调用链路
```
1. 客户端请求 → 网关
   TraceId: ROOT-GW-a1b2c3d4e5f67890

2. 网关 → API中心
   TraceId: ROOT-GW-a1b2c3d4e5f67890.API-12345678

3. API中心 → 业务服务
   TraceId: ROOT-GW-a1b2c3d4e5f67890.API-12345678.BIZ-87654321

4. 业务服务 → 基础服务
   TraceId: ROOT-GW-a1b2c3d4e5f67890.API-12345678.BIZ-87654321.BASE-11223344
```

### 层级关系分析
- **层级0**：ROOT-GW-a1b2c3d4e5f67890（网关入口）
- **层级1**：API-12345678（API中心）
- **层级2**：BIZ-87654321（业务服务）
- **层级3**：BASE-11223344（基础服务）

## 最佳实践

### 1. 服务标识规范
- **GW**：网关服务
- **API**：API中心
- **BIZ**：业务服务
- **BASE**：基础服务
- **LOG**：日志服务

### 2. 日志输出规范
- 关键业务节点必须输出日志
- 异常情况必须包含TraceId
- 性能敏感操作记录耗时
- 外部调用前后记录日志

### 3. 异步处理规范
- 异步线程必须传递TraceId
- 使用TraceContext工具类管理
- 避免TraceId丢失

## 故障排查

### 1. TraceId丢失
- 检查过滤器配置
- 确认MDC设置
- 验证异步传递

### 2. 层级关系错误
- 检查服务标识配置
- 验证请求头传递
- 确认Feign拦截器

### 3. 日志格式问题
- 检查logback配置
- 验证MDC变量名
- 确认日志级别

## 监控和分析

### 1. 链路追踪分析
- 根据TraceId查询完整调用链路
- 分析服务调用层级关系
- 识别性能瓶颈节点

### 2. 日志聚合
- 基于TraceId聚合相关日志
- 构建完整的业务处理流程
- 快速定位问题根因

### 3. 性能监控
- 统计各层级服务耗时
- 分析调用链路性能
- 优化服务调用路径
