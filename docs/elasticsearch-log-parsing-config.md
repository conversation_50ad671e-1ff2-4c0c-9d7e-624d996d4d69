# Elasticsearch日志解析配置

## 概述

本文档提供了用于解析包含TraceId的日志格式的Elasticsearch配置，包括正则表达式、字段映射和索引模板。

## 日志格式

### 更新后的日志格式
```
2025-07-03 10:30:15.123 +08:00 frp-gateway INFO [12345] [reactor-http-nio-2|ROOT-GW-a1b2c3d4e5f67890] [cn.need.cloud.gateway.filter.AccessLogFilter] [AccessLogFilter.java:75] : Gateway Access Log - TraceId: ROOT-GW-a1b2c3d4e5f67890
```

### 格式说明
- **timestamp**: `2025-07-03 10:30:15.123 +08:00`
- **service_name**: `frp-gateway`
- **log_level**: `INFO`
- **pid**: `12345`
- **thread_name**: `reactor-http-nio-2|ROOT-GW-a1b2c3d4e5f67890` (包含TraceId)
- **class_name**: `cn.need.cloud.gateway.filter.AccessLogFilter`
- **java_line**: `AccessLogFilter.java:75`
- **log_info**: `Gateway Access Log - TraceId: ROOT-GW-a1b2c3d4e5f67890`

## 正则表达式配置

### 更新后的正则表达式
```regex
(?<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3} (?:Z|[+-]\d{2}:\d{2}|[+-]\d{2}))\s+(?<service_name>\S+)\s+(?<log_level>[A-Z]+)\s+\[(?<pid>\d+)\]\s+\[(?<thread_name>[^|]+)\|(?<trace_id>[^\]]+)\]\s+\[(?<class_name>[^\]]+)\]\s+\[(?<java_line>[^\]]+)\]\s+:\s+(?<log_info>.*)
```

### 字段说明
- `timestamp`: 时间戳（包含时区信息）
- `service_name`: 服务名称
- `log_level`: 日志级别
- `pid`: 进程ID
- `thread_name`: 线程名称
- `trace_id`: 链路追踪ID
- `class_name`: 类名
- `java_line`: 文件名和行号
- `log_info`: 日志内容

### 兼容性正则（支持无TraceId的旧日志）
```regex
(?<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3} (?:Z|[+-]\d{2}:\d{2}|[+-]\d{2}))\s+(?<service_name>\S+)\s+(?<log_level>[A-Z]+)\s+\[(?<pid>\d+)\]\s+\[(?<thread_name>(?:[^|]+\|(?<trace_id>[^\]]+)|[^\]]+))\]\s+\[(?<class_name>[^\]]+)\]\s+\[(?<java_line>[^\]]+)\]\s+:\s+(?<log_info>.*)
```

## Logstash配置

### Grok模式配置
```ruby
# logstash/patterns/java_logs
JAVA_TIMESTAMP %{YEAR}-%{MONTHNUM}-%{MONTHDAY} %{TIME} (?:Z|[+-]%{HOUR}:?%{MINUTE}?)
JAVA_THREAD_WITH_TRACE (?<thread_name>[^|]+)\|(?<trace_id>[^\]]+)
JAVA_THREAD_WITHOUT_TRACE (?<thread_name>[^\]]+)
JAVA_THREAD_FIELD (?:%{JAVA_THREAD_WITH_TRACE}|%{JAVA_THREAD_WITHOUT_TRACE})

# 主要模式
JAVA_LOG_PATTERN %{JAVA_TIMESTAMP:timestamp} %{DATA:service_name} %{LOGLEVEL:log_level} \[%{POSINT:pid}\] \[%{JAVA_THREAD_FIELD}\] \[%{DATA:class_name}\] \[%{DATA:java_line}\] : %{GREEDYDATA:log_info}
```

### Logstash过滤器配置
```ruby
filter {
  if [fields][log_type] == "java_application" {
    grok {
      match => { "message" => "%{JAVA_LOG_PATTERN}" }
    }
    
    # 解析时间戳
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS Z", "yyyy-MM-dd HH:mm:ss.SSS XX" ]
      target => "@timestamp"
    }
    
    # 处理TraceId字段
    if [trace_id] {
      mutate {
        # 提取根TraceId
        add_field => { "root_trace_id" => "%{trace_id}" }
      }
      
      # 计算调用层级
      ruby {
        code => "
          trace_id = event.get('trace_id')
          if trace_id
            depth = trace_id.count('.')
            event.set('trace_depth', depth)
            
            # 提取根TraceId
            root_id = trace_id.split('.').first
            event.set('root_trace_id', root_id)
            
            # 提取服务调用链
            services = trace_id.split('.').map { |part| part.split('-').first }
            event.set('service_chain', services)
          end
        "
      }
    } else {
      mutate {
        add_field => { "trace_id" => "NO_TRACE" }
        add_field => { "root_trace_id" => "NO_TRACE" }
        add_field => { "trace_depth" => 0 }
      }
    }
    
    # 转换数据类型
    mutate {
      convert => { "pid" => "integer" }
      convert => { "trace_depth" => "integer" }
    }
    
    # 添加标签
    mutate {
      add_tag => [ "java_log", "parsed" ]
    }
  }
}
```

## Elasticsearch索引模板

### 索引模板配置
```json
{
  "index_patterns": ["java-logs-*"],
  "template": {
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.refresh_interval": "5s"
    },
    "mappings": {
      "properties": {
        "@timestamp": {
          "type": "date"
        },
        "timestamp": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss.SSS Z||yyyy-MM-dd HH:mm:ss.SSS XX"
        },
        "service_name": {
          "type": "keyword"
        },
        "log_level": {
          "type": "keyword"
        },
        "pid": {
          "type": "integer"
        },
        "thread_name": {
          "type": "keyword"
        },
        "trace_id": {
          "type": "keyword"
        },
        "root_trace_id": {
          "type": "keyword"
        },
        "trace_depth": {
          "type": "integer"
        },
        "service_chain": {
          "type": "keyword"
        },
        "class_name": {
          "type": "keyword"
        },
        "java_line": {
          "type": "keyword"
        },
        "log_info": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "message": {
          "type": "text"
        }
      }
    }
  }
}
```

## Kibana可视化配置

### 推荐的Kibana字段配置
```json
{
  "fields": [
    {
      "name": "@timestamp",
      "type": "date",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "service_name",
      "type": "string",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "log_level",
      "type": "string",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "trace_id",
      "type": "string",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "root_trace_id",
      "type": "string",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "trace_depth",
      "type": "number",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "class_name",
      "type": "string",
      "searchable": true,
      "aggregatable": true
    },
    {
      "name": "log_info",
      "type": "string",
      "searchable": true,
      "aggregatable": false
    }
  ]
}
```

### 常用查询示例

#### 1. 根据TraceId查询完整调用链
```json
{
  "query": {
    "term": {
      "root_trace_id": "ROOT-GW-a1b2c3d4e5f67890"
    }
  },
  "sort": [
    {
      "@timestamp": {
        "order": "asc"
      }
    },
    {
      "trace_depth": {
        "order": "asc"
      }
    }
  ]
}
```

#### 2. 查询特定服务的错误日志
```json
{
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "service_name": "frp-business"
          }
        },
        {
          "term": {
            "log_level": "ERROR"
          }
        }
      ]
    }
  }
}
```

#### 3. 统计各服务的调用层级分布
```json
{
  "aggs": {
    "services": {
      "terms": {
        "field": "service_name"
      },
      "aggs": {
        "trace_depth_stats": {
          "stats": {
            "field": "trace_depth"
          }
        }
      }
    }
  }
}
```

## 测试验证

### 测试日志样例
```
2025-07-03 10:30:15.123 +08:00 frp-gateway INFO [12345] [reactor-http-nio-2|ROOT-GW-a1b2c3d4e5f67890] [cn.need.cloud.gateway.filter.AccessLogFilter] [AccessLogFilter.java:75] : Gateway Access Log - Method: POST, Path: /api/v1/orders

2025-07-03 10:30:15.156 +08:00 frp-api-center INFO [23456] [http-nio-10140-exec-1|ROOT-GW-a1b2c3d4e5f67890.API-12345678] [cn.need.cloud.apicenter.controller.OrderController] [OrderController.java:45] : 接收到订单创建请求

2025-07-03 10:30:15.189 +08:00 frp-business INFO [34567] [http-nio-10030-exec-2|ROOT-GW-a1b2c3d4e5f67890.API-12345678.BIZ-87654321] [cn.need.cloud.biz.service.OrderService] [OrderServiceImpl.java:67] : 开始处理订单创建业务逻辑

2025-07-03 10:30:15.234 +08:00 frp-business INFO [34567] [http-nio-10030-exec-2|NO_TRACE] [cn.need.cloud.biz.service.OrderService] [OrderServiceImpl.java:89] : 订单创建完成
```

### 验证步骤
1. 配置Logstash使用新的Grok模式
2. 重启Logstash服务
3. 发送测试日志到Elasticsearch
4. 在Kibana中验证字段解析
5. 测试TraceId相关查询

## 注意事项

1. **时区处理**：确保时间戳格式包含正确的时区信息
2. **字段长度**：TraceId可能较长，确保Elasticsearch字段配置支持
3. **性能考虑**：TraceId字段建议使用keyword类型以提高查询性能
4. **兼容性**：配置支持新旧日志格式的兼容性解析
5. **索引策略**：根据日志量配置合适的索引分片和副本数量
