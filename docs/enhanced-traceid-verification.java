package cn.need.framework.test;

import cn.need.framework.common.core.trace.TraceContext;
import cn.need.framework.common.core.trace.TraceLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 增强版链路追踪功能验证类
 * 
 * 用于验证TraceId生成、传递、日志输出等功能
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
public class EnhancedTraceIdVerification {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedTraceIdVerification.class);
    private static final TraceLogger.TraceLoggerWrapper traceLog = TraceLogger.getLogger(EnhancedTraceIdVerification.class);
    
    public static void main(String[] args) {
        System.out.println("=== 增强版链路追踪功能验证 ===\n");
        
        // 1. 验证TraceId生成
        verifyTraceIdGeneration();
        
        // 2. 验证层级关系
        verifyTraceHierarchy();
        
        // 3. 验证日志输出
        verifyLogOutput();
        
        // 4. 验证异步传递
        verifyAsyncTraceId();
        
        // 5. 验证工具方法
        verifyUtilityMethods();
        
        System.out.println("=== 验证完成 ===");
    }
    
    /**
     * 验证TraceId生成功能
     */
    private static void verifyTraceIdGeneration() {
        System.out.println("1. TraceId生成验证:");
        
        // 清除现有TraceId
        TraceContext.clearTraceId();
        System.out.println("   初始状态 - hasTraceId: " + TraceContext.hasTraceId());
        
        // 生成根TraceId
        String rootTraceId = TraceContext.generateChildTraceId("TEST");
        TraceContext.setTraceId(rootTraceId);
        System.out.println("   根TraceId: " + rootTraceId);
        System.out.println("   当前TraceId: " + TraceContext.getTraceId());
        System.out.println("   hasTraceId: " + TraceContext.hasTraceId());
        
        // 生成子TraceId
        String childTraceId = TraceContext.generateChildTraceId("CHILD");
        System.out.println("   子TraceId: " + childTraceId);
        
        System.out.println("   ✓ TraceId生成功能正常\n");
    }
    
    /**
     * 验证层级关系
     */
    private static void verifyTraceHierarchy() {
        System.out.println("2. 层级关系验证:");
        
        // 模拟网关入口
        String gatewayTraceId = "ROOT-GW-a1b2c3d4e5f67890";
        TraceContext.setTraceId(gatewayTraceId);
        System.out.println("   网关TraceId: " + gatewayTraceId);
        System.out.println("   层级深度: " + TraceContext.getTraceDepth());
        System.out.println("   根TraceId: " + TraceContext.getRootTraceId());
        
        // 模拟API中心调用
        String apiTraceId = TraceContext.generateChildTraceId("API");
        TraceContext.setTraceId(apiTraceId);
        System.out.println("   API中心TraceId: " + apiTraceId);
        System.out.println("   层级深度: " + TraceContext.getTraceDepth());
        System.out.println("   根TraceId: " + TraceContext.getRootTraceId());
        
        // 模拟业务服务调用
        String bizTraceId = TraceContext.generateChildTraceId("BIZ");
        TraceContext.setTraceId(bizTraceId);
        System.out.println("   业务服务TraceId: " + bizTraceId);
        System.out.println("   层级深度: " + TraceContext.getTraceDepth());
        System.out.println("   根TraceId: " + TraceContext.getRootTraceId());
        
        System.out.println("   ✓ 层级关系功能正常\n");
    }
    
    /**
     * 验证日志输出
     */
    private static void verifyLogOutput() {
        System.out.println("3. 日志输出验证:");
        
        // 设置TraceId
        String testTraceId = "ROOT-TEST-1234567890abcdef.API-12345678.BIZ-87654321";
        TraceContext.setTraceId(testTraceId);
        
        // 使用TraceLogger输出日志
        System.out.println("   使用TraceLogger输出:");
        traceLog.info("这是一条INFO级别的测试日志，参数：{}", "test-param");
        traceLog.warn("这是一条WARN级别的测试日志");
        traceLog.error("这是一条ERROR级别的测试日志");
        
        // 使用传统Logger输出日志
        System.out.println("   使用传统Logger输出:");
        log.info("{} 这是传统Logger的INFO日志", TraceContext.getTraceInfo());
        log.warn("{} 这是传统Logger的WARN日志", TraceContext.getTraceInfo());
        
        System.out.println("   ✓ 日志输出功能正常\n");
    }
    
    /**
     * 验证异步传递
     */
    private static void verifyAsyncTraceId() {
        System.out.println("4. 异步传递验证:");
        
        String mainTraceId = "ROOT-ASYNC-test123456789.MAIN-abcdefgh";
        TraceContext.setTraceId(mainTraceId);
        System.out.println("   主线程TraceId: " + TraceContext.getTraceId());
        
        // 验证runWithTraceId
        String asyncTraceId = TraceContext.generateChildTraceId("ASYNC");
        TraceContext.runWithTraceId(asyncTraceId, () -> {
            System.out.println("   异步执行中TraceId: " + TraceContext.getTraceId());
            traceLog.info("异步任务执行中");
        });
        
        System.out.println("   异步执行后主线程TraceId: " + TraceContext.getTraceId());
        
        // 验证callWithTraceId
        String result = TraceContext.callWithTraceId(asyncTraceId, () -> {
            traceLog.info("带返回值的异步任务执行中");
            return "异步执行结果";
        });
        System.out.println("   异步执行结果: " + result);
        
        System.out.println("   ✓ 异步传递功能正常\n");
    }
    
    /**
     * 验证工具方法
     */
    private static void verifyUtilityMethods() {
        System.out.println("5. 工具方法验证:");
        
        // 测试各种工具方法
        String complexTraceId = "ROOT-UTIL-1234567890abcdef.API-12345678.BIZ-87654321.DB-11223344";
        TraceContext.setTraceId(complexTraceId);
        
        System.out.println("   复杂TraceId: " + complexTraceId);
        System.out.println("   getTraceId(): " + TraceContext.getTraceId());
        System.out.println("   hasTraceId(): " + TraceContext.hasTraceId());
        System.out.println("   getTraceDepth(): " + TraceContext.getTraceDepth());
        System.out.println("   getRootTraceId(): " + TraceContext.getRootTraceId());
        System.out.println("   getTraceInfo(): " + TraceContext.getTraceInfo());
        
        // 测试清除功能
        TraceContext.clearTraceId();
        System.out.println("   清除后hasTraceId(): " + TraceContext.hasTraceId());
        System.out.println("   清除后getTraceId(): '" + TraceContext.getTraceId() + "'");
        System.out.println("   清除后getTraceInfo(): " + TraceContext.getTraceInfo());
        
        System.out.println("   ✓ 工具方法功能正常\n");
    }
}

/**
 * 验证清单
 */
class EnhancedTraceIdChecklist {
    
    /**
     * 功能验证清单
     */
    public static void printChecklist() {
        System.out.println("=== 增强版链路追踪验证清单 ===");
        System.out.println();
        
        System.out.println("□ 1. TraceId生成功能");
        System.out.println("  □ 根TraceId生成（ROOT-{SERVICE}-{16位ID}）");
        System.out.println("  □ 子TraceId生成（{父TraceId}.{SERVICE}-{8位ID}）");
        System.out.println("  □ TraceId设置和获取");
        System.out.println();
        
        System.out.println("□ 2. 层级关系管理");
        System.out.println("  □ 调用链路层级计算");
        System.out.println("  □ 根TraceId提取");
        System.out.println("  □ 层级深度统计");
        System.out.println();
        
        System.out.println("□ 3. 日志输出增强");
        System.out.println("  □ TraceLogger自动包含TraceId");
        System.out.println("  □ 传统Logger手动添加TraceId");
        System.out.println("  □ 日志格式统一");
        System.out.println();
        
        System.out.println("□ 4. 异步传递支持");
        System.out.println("  □ runWithTraceId方法");
        System.out.println("  □ callWithTraceId方法");
        System.out.println("  □ TraceId自动恢复");
        System.out.println();
        
        System.out.println("□ 5. 网关响应式支持");
        System.out.println("  □ GatewayTraceLogFilter");
        System.out.println("  □ Reactor Context传递");
        System.out.println("  □ 请求头注入");
        System.out.println();
        
        System.out.println("□ 6. 配置文件更新");
        System.out.println("  □ frp-api-center logback配置");
        System.out.println("  □ frp-business logback配置");
        System.out.println("  □ frp-gateway logback配置");
        System.out.println("  □ frp-base logback配置");
        System.out.println();
        
        System.out.println("□ 7. 服务间调用");
        System.out.println("  □ TraceIdRequestInterceptor");
        System.out.println("  □ Feign请求头传递");
        System.out.println("  □ 跨服务TraceId连续性");
        System.out.println();
    }
}
