#!/bin/bash
source /etc/profile

# create directory if not exist
if [ ! -d "/app/frp-business" ];then
  mkdir -p /app/frp-business
fi

# clear application log
echo Clearing application.log ...
rm -rf logs
mkdir -p logs/frp-business
touch logs/frp-business/application.log

# stop service
echo Stopping frp-business ...
docker compose -f frp-business.yaml down

# start service
echo Starting frp-business ...
docker compose -f frp-business.yaml up -d

# print log, auto abort when 'JVM running for' is encountered(start successfully)
tail -200f logs/frp-business/application.log | sed '/JVM running for/ q'
