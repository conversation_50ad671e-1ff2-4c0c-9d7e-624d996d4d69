version: '3'

networks:
  uneed-net-overlay:
    external: true

services:
  uneed-product:
    image: apache/skywalking-java-agent:9.1.0-java17
    hostname: frp-business
    container_name: frp-business
    restart: always
    privileged: true
    environment:
      - TZ=Asia/Shanghai
      - SW_AGENT_NAME=frp-business
      #- SW_AGENT_COLLECTOR_BACKEND_SERVICES=skywalking-oap:11800
      - JAVA_OPTS=-Xms256m -Xmx512m
    ports:
      - 10030:10030
    networks:
      - uneed-net-overlay
    volumes:
      - /app/frp-business/frp-business-boot.jar:/app/frp-business-boot.jar
      - /app/frp-business/logs:/skywalking/logs:rw
    command:
      /bin/bash -c "java -DNACOS_HOST=nacosServer -DNACOS_PORT=8848 -DFRP_MYSQL_HOST=mysqlNode -DFRP_MYSQL_PORT=3306 -DFRP_MYSQL_DB=frp-business -jar /app/frp-business-boot.jar --info"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS http://localhost:10030/actuator/health || exit 1"]
      interval: 1m
      timeout: 10s
      retries: 3
