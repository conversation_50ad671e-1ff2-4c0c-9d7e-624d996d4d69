<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.need.cloud</groupId>
        <artifactId>frp-business</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>

    <artifactId>frp-business-boot</artifactId>
    <packaging>jar</packaging>
    <name>frp-business-boot</name>
    <description>the business server Center for uneed need-cloud</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-warehouse</artifactId>
            <version>${version}</version>
        </dependency>
        <!-- alibaba nacos-->
        <!-- 注册中心、配置中心-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <!--开启监控检查-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- spring cloud -->
        <!-- openfeign 服务调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!--服务依赖，版本与当前项目保持一致 -->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>frp-business-server</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Uneed starter 依赖-->
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-tenant</artifactId>
        </dependency>
        <!-- sky walking logback support -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.16.0</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>frp-business</finalName>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!--配置true，指的是下面的文件会替换文件中的@xxx@占位符，且下面必须配置插件maven-resources-plugin -->
                <filtering>true</filtering>
                <includes>
                    <include>application.yaml</include>
                    <include>application-${ACTIVE_ENV}.yaml</include>
                    <include>application.yml</include>
                    <include>application-${ACTIVE_ENV}.yml</include>
                    <include>bootstrap.yaml</include>
                    <include>bootstrap-${ACTIVE_ENV}.yaml</include>
                    <include>bootstrap.yml</include>
                    <include>bootstrap-${ACTIVE_ENV}.yml</include>
                </includes>
            </resource>
            <!--配置不需要占位符替换的文件，配置了之后才会copy到target中 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>logback-spring.xml</include>
                    <include>META-INF/**</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <propertiesEncoding>UTF-8</propertiesEncoding>
                    <encoding>UTF-8</encoding>
                    <!--设置为true的话，则采用默认占位符@或者$来进行替换，只能做简单替换，并不能支持${xx:1}这种默认的语法,这种语法只支持读取系统或环境变量 -->
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>