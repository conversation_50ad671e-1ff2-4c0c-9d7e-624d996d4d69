package cn.need.cloud.biz.controller.binlocation;

import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.service.binlocation.BinLocationExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 库位详情导出
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@RestController
@RequestMapping("/api/biz/binlocation/export")
@Tag(name = "库位详情-导出")
@Slf4j
@RequiredArgsConstructor
public class BinLocationExportController {

    private final BinLocationExportService service;

    @Operation(summary = "库位详情-export", description = "导出库位详情列表(仅导出库存>0的数据)")
    @PostMapping("/list")
    public void export(@RequestBody @Validated @Parameter(description = "搜索条件参数", required = true) BinLocationQuery query, HttpServletResponse response) {
        // 直接执行导出，不返回Result对象
        service.export(query, response);
    }
} 