package cn.need.cloud.biz.model.param.inbound.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 入库请求详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "入库请求详情 vo对象")
public class InboundRequestDetailUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 入库请求Id
     */
    @Schema(description = "入库请求Id")
    private Long inboundRequestId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 详情请求参考编号
     */
    @Schema(description = "详情请求参考编号")
    private String detailRequestRefNum;

    /**
     * 详情类型
     */
    @Schema(description = "详情类型")
    private String detailType;

}