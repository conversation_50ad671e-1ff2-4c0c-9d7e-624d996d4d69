package cn.need.cloud.biz.model.param.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * OtcWorkorderStartRollbackUpdateParam
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "拣货单 Finish 对象")
public class WorkorderFinishUpdateParam implements Serializable {

    @Schema(description = "工单id集合")
    @NotEmpty(message = "WorkOrder idList is not empty")
    @NotNull(message = "WorkOrder idList is not null")
    private List<Long> idList;
}
