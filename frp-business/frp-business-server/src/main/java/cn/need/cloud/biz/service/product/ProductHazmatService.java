package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.model.entity.product.ProductHazmat;
import cn.need.cloud.biz.model.param.product.create.ProductHazmatCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductHazmatUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductHazmatQuery;
import cn.need.cloud.biz.model.vo.product.ProductHazmatVO;
import cn.need.cloud.biz.model.vo.product.page.ProductHazmatPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;

import java.util.List;

/**
 * <p>
 * 产品危险品服务接口
 * </p>
 * <p>
 * 该接口提供产品危险品信息管理的核心业务功能，包括危险品信息的创建、查询、更新和删除等。
 * 危险品信息对于物流和仓储管理至关重要，影响产品的存储条件、运输方式和包装要求等。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 产品危险品信息管理（创建、更新、查询、删除）
 * 2. 根据产品ID获取关联的危险品信息
 * 3. 危险品信息的分页查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface ProductHazmatService extends SuperService<ProductHazmat> {

    /**
     * 创建产品危险品信息
     * <p>
     * 根据提供的参数创建新的产品危险品信息记录。
     * 危险品信息包括危险品类别、UN编号、包装组别等国际标准化的危险品描述。
     * </p>
     *
     * @param createParam 危险品创建参数，包含产品ID、危险品类别、UN编号等信息
     * @return 创建成功的危险品信息ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ProductHazmatCreateParam createParam);


    /**
     * 更新产品危险品信息
     * <p>
     * 根据提供的参数更新已有的产品危险品信息记录。
     * 可以修改危险品类别、UN编号、包装组别等信息。
     * </p>
     *
     * @param updateParam 危险品更新参数，包含需要更新的危险品信息
     * @return 更新影响的记录数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(ProductHazmatUpdateParam updateParam);

    /**
     * 根据查询条件获取危险品信息列表
     * <p>
     * 查询符合条件的产品危险品信息记录，不包含分页信息。
     * 可用于特定业务场景下的危险品数据查询。
     * </p>
     *
     * @param query 查询条件对象，包含产品ID、危险品类别等筛选条件
     * @return 返回符合条件的危险品信息列表
     */
    List<ProductHazmatPageVO> listByQuery(ProductHazmatQuery query);

    /**
     * 分页查询危险品信息
     * <p>
     * 根据查询条件和分页参数查询产品危险品信息。
     * 返回的结果包含分页信息和危险品记录列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的危险品信息列表
     */
    PageData<ProductHazmatPageVO> pageByQuery(PageSearch<ProductHazmatQuery> search);

    /**
     * 根据ID获取危险品详情
     * <p>
     * 查询指定ID的产品危险品记录详细信息。
     * </p>
     *
     * @param id 危险品信息ID
     * @return 返回危险品信息视图对象
     */
    ProductHazmatVO detailById(Long id);

    /**
     * 根据产品ID获取危险品信息
     * <p>
     * 查询指定产品关联的危险品信息。
     * 一个产品通常只关联一条危险品信息记录。
     * </p>
     *
     * @param id 产品ID
     * @return 返回产品危险品实体对象，如果不存在则返回null
     */
    ProductHazmat getByProductId(Long id);

    /**
     * 删除危险品信息
     * <p>
     * 根据指定的参数删除产品危险品信息。
     * 删除操作通常是逻辑删除，保留历史记录。
     * </p>
     *
     * @param deletedNoteParam 删除参数，包含ID和删除备注信息
     * @return 删除影响的记录数
     */
    Integer removeHazmat(DeletedNoteParam deletedNoteParam);
}