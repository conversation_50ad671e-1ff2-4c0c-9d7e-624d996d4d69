package cn.need.cloud.biz.model.query.otb.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/***
 * ReLabel打印查询条件
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
@Schema(description = "MarkReLabelPrint 产品Barcode 打印 query对象")
public class OtbPickingSlipMarkReLabelPrintByBarcodeQuery {

    /**
     * OTB拣货单id
     */
    @Schema(description = "OTB拣货单id")
    @NotNull(message = "otbPickingSlipId is must not null")
    private Long otbPickingSlipId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    @NotNull(message = "productId is must not null")
    private Long productId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @NotNull(message = "qty is must not null")
    @Min(value = 1, message = "qty must be greater than 1")
    private Integer qty;

    /**
     * 产品Barcode
     */
    @Schema(description = "产品Barcode")
    @NotBlank(message = "productBarcode is must not blank")
    private String productBarcode;

    /**
     * 产品渠道SKU
     */
    @Schema(description = "产品渠道SKU")
    @NotBlank(message = "productChannelSku is must not blank")
    private String productChannelSku;
}
