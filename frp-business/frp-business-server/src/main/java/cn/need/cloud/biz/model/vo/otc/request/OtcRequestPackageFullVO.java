package cn.need.cloud.biz.model.vo.otc.request;

import cn.need.cloud.biz.jackson.BigDecimalFormat;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = " Otc请求包裹详情 VO对象")
public class OtcRequestPackageFullVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "行号")
    @NotNull(message = "lineNum must not be null.")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

    @Schema(description = "跟踪号")
    private String trackingNum;

    @Schema(description = "是否快递发货")
    private Boolean shipExpressFlag;

    @Schema(description = "是否是多箱包裹")
    private Boolean multiBoxPackageFlag;

    @Schema(description = "运输方式")
    private String shipMethod;

    @Schema(description = "运输承运商")
    private String shipCarrier;

    /**
     * 运输箱子-长
     */
    @Schema(description = "运输箱子-长")
    @NotNull(message = "shipSizeLength must not be null.")
    @DecimalMin(value = "0", inclusive = false, message = "shipSizeLength must be greater than 0")
    @BigDecimalFormat
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    @Schema(description = "运输箱子-宽")
    @NotNull(message = "shipSizeWidth must not be null.")
    @DecimalMin(value = "0", inclusive = false, message = "shipSizeWidth must be greater than 0")
    @BigDecimalFormat
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    @Schema(description = "运输箱子-高")
    @NotNull(message = "shipSizeHeight must not be null.")
    @DecimalMin(value = "0", inclusive = false, message = "shipSizeHeight must be greater than 0")
    @BigDecimalFormat
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    @Schema(description = "运输箱子-重量")
    @NotNull(message = "shipSizeWeight must not be null.")
    @DecimalMin(value = "0", inclusive = false, message = "shipSizeWeight must be greater than 0")
    @BigDecimalFormat
    private BigDecimal shipSizeWeight;

    /**
     * 运输箱子-长度单位
     */
    @Schema(description = "运输箱子-长度单位")
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    @Schema(description = "运输箱子-重量单位")
    private String shipSizeWeightUnit;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "标签")
    @Valid
    @NotEmpty(message = "packageLabelList cannot be empty")
    private List<OtcRequestPackageLabelFullVO> labelList;

    @Schema(description = "详情")
    @Valid
    @NotEmpty(message = "packageDetailList cannot be empty")
    private List<OtcRequestPackageDetailFullVO> detailList;


    @Schema(description = "包裹多箱中的产品ID")
    private Long packageMultiboxProductId;

    @Schema(description = "UPC")
    private String packageMultiboxUpc;

    @Schema(description = "包裹多箱行号")
    private Integer packageMultiboxLineNum;

    @Schema(description = "多箱版本号")
    private Integer packageMultiboxVersionInt;

    @Schema(description = "基础信息")
    private BaseProductVO packageMultiboxProductVo;


}
