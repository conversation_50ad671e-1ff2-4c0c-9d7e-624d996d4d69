package cn.need.cloud.biz.model.query.profile;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 仓库档案 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库档案 query对象")
public class ProfileWarehouseQuery extends SuperQuery {

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型集合")
    @Condition(value = Keyword.IN, fields = {"serviceType"})
    private List<String> serviceTypeList;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码")
    private String categoryCode;

    /**
     * 分类代码集合
     */
    @Schema(description = "分类代码集合")
    private List<String> categoryCodeList;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述")
    private String categoryDesc;

    /**
     * 分类描述集合
     */
    @Schema(description = "分类描述集合")
    private List<String> categoryDescList;

    /**
     * 值类型
     */
    @Schema(description = "值类型")
    private String valueType;

    /**
     * 值类型
     */
    @Schema(description = "值类型集合")
    @Condition(value = Keyword.IN, fields = {"valueType"})
    private List<String> valueTypeList;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 名称集合
     */
    @Schema(description = "名称集合")
    private List<String> nameList;

    /**
     * 值
     */
    @Schema(description = "值")
    private String value;

    /**
     * 值
     */
    @Schema(description = "值")
    private List<String> valueList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 激活标志
     */
    @Schema(description = "激活标志")
    private Boolean activeFlag;

    /**
     * 代码
     */
    @Schema(description = "代码")
    private String code;

    /**
     * 代码集合
     */
    @Schema(description = "代码集合")
    private List<String> codeList;


}