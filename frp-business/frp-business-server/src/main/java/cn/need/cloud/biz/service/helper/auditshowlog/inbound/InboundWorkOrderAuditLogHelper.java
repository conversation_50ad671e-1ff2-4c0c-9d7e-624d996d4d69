package cn.need.cloud.biz.service.helper.auditshowlog.inbound;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.framework.common.core.lang.ObjectUtil;

import java.util.List;

/**
 * 入库工单日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class InboundWorkOrderAuditLogHelper {

    private InboundWorkOrderAuditLogHelper() {

    }

    public static void recordLog(List<InboundWorkorder> inboundWorkorderList, String type, String note, String description) {
        inboundWorkorderList.forEach(item -> recordLog(item, type, note, description));
    }

    public static void recordLog(List<InboundWorkorder> inboundWorkorderList) {
        inboundWorkorderList.forEach(InboundWorkOrderAuditLogHelper::recordLog);
    }

    public static void recordLog(InboundWorkorder inboundWorkorder) {
        recordLog(
                inboundWorkorder,
                BaseTypeLogEnum.STATUS.getType(),
                null,
                null
        );
    }

    public static void recordLog(InboundWorkorder inboundWorkorder, String description) {
        recordLog(
                inboundWorkorder,
                BaseTypeLogEnum.STATUS.getType(),
                null,
                description
        );
    }

    public static void recordLog(InboundWorkorder inboundWorkorder, String type, String note, String description) {
        recordLog(
                inboundWorkorder,
                inboundWorkorder.getInboundWorkorderStatus(),
                type,
                note,
                description
        );
    }

    public static void recordWithStatus(InboundWorkorder inboundWorkorder, String status, String type, String description) {
        recordLog(
                inboundWorkorder,
                status,
                type,
                null,
                description
        );
    }

    public static void recordLog(InboundWorkorder inboundWorkorder, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(inboundWorkorder)
                .with(ObjectUtil.isNotEmpty(status), AuditShowLog::setEvent, status)
                .with(ObjectUtil.isNotEmpty(description), AuditShowLog::setDescription, description)
                .with(ObjectUtil.isNotEmpty(inboundWorkorder.getWarehouseId()), AuditShowLog::setWarehouseId, inboundWorkorder.getWarehouseId())
                .with(ObjectUtil.isNotEmpty(note), AuditShowLog::setNote, note)
                .with(ObjectUtil.isNotEmpty(type), AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }
}
