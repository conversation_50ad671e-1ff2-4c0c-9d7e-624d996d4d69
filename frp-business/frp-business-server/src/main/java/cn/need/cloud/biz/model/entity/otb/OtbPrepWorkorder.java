package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * OTB预提工单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_prep_workorder")
public class OtbPrepWorkorder extends PrepWorkorderModel {


    @Serial
    private static final long serialVersionUID = 8837647845638608728L;
    /**
     * otb工单id
     */
    @TableField("otb_workorder_id")
    private Long otbWorkorderId;

    /**
     * otb 工单详情id
     */
    @TableField("otb_workorder_detail_id")
    private Long otbWorkorderDetailId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 库存预定id
     */
    @TableField("inventory_reserve_id")
    private Long inventoryReserveId;

    /**
     * 上架数量
     */
    @TableField("putaway_qty")
    private Integer putawayQty;

    /**
     * 预工单类型
     */
    @TableField("otb_prep_workorder_status")
    private String otbPrepWorkorderStatus;

    /**
     * 预工单类型
     */
    @TableField("prep_workorder_type")
    private String prepWorkorderType;

    /**
     * otb预拣货单id
     */
    @TableField("otb_prep_picking_slip_id")
    private Long otbPrepPickingSlipId;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 产品类型
     */
    @TableField("detail_product_type")
    private String detailProductType;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @TableField("product_barcode")
    private String productBarcode;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;

    /**
     * 预工单产品版本
     */
    @TableField("prep_workorder_version_int")
    private Integer prepWorkorderVersionInt;

    /**
     * otb请求id
     */
    @TableField("otb_request_id")
    private Long otbRequestId;

    /**
     * 渠道要求的需要贴的产品标识SKU
     */
    @TableField("product_channel_sku")
    private String productChannelSku;

    /**
     * 产品所属PartnerId
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * otb预工单产品类型
     */
    @TableField("prep_workorder_product_type")
    private String prepWorkorderProductType;


    /**
     * 发货类型
     */
    @TableField("ship_type")
    private ShipTypeEnum shipType;

    /**
     * RI文件数据
     */
    @TableField("routing_instruction_file_file_data")
    private String routingInstructionFileFileData;

    /**
     * RI文件扩展名
     */
    @TableField("routing_instruction_file_file_extension")
    private String routingInstructionFileFileExtension;

    /**
     * RI文件类型
     */
    @TableField("routing_instruction_file_file_type")
    private FileDataTypeEnum routingInstructionFileFileType;

    /**
     * RI文件纸张类型
     */
    @TableField("routing_instruction_file_paper_type")
    private PaperTypeEnum routingInstructionFilePaperType;

}
