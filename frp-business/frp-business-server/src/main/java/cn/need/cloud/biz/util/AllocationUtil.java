package cn.need.cloud.biz.util;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.model.entity.base.ProductModel;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.BasePutAwayVO;
import cn.need.cloud.biz.model.vo.base.ProductBinLocationPickVO;
import cn.need.cloud.biz.model.vo.base.ReadyToGoProductBinLocationPickVO;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.model.IdModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.*;

/***
 * 数量分配工具类
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public class AllocationUtil {

    private AllocationUtil() {
    }


    // /**
    //  * 校验并分配拣货数量
    //  *
    //  * @param pickList 需要拣货的数据
    //  * @param qty      拣货数量
    //  */
    // public static void checkAndAllocationPickQty(List<? extends BasePickVO> pickList, int qty) {
    //     // 赋值拣货前的数量
    //     pickList.forEach(obj -> obj.setPickedBeforeQty(obj.getPickedQty()));
    //     checkAndAllocationQty(qty, () -> allocationPickQty(pickList, qty));
    // }
    //
    //
    // /**
    //  * 校验并分配发货数量
    //  *
    //  * @param shippedList 需要拣货的数据
    //  * @param qty         拣货数量
    //  */
    // public static void checkAndAllocationShippedQty(List<? extends BaseShippedVO> shippedList, int qty) {
    //     // 赋值拣货前的数量
    //     // 赋值拣货前的数量
    //     shippedList.forEach(obj -> obj.setShippedBeforeQty(obj.getShippedQty()));
    //     checkAndAllocationQty(qty, () -> allocationQty(shippedList, qty));
    // }
    //
    //
    // /**
    //  * 校验并分配打包数量
    //  *
    //  * @param packedList 需要拣货的数据
    //  * @param qty        拣货数量
    //  */
    // public static void checkAndAllocationPackedQty(List<? extends BasePackedVO> packedList, int qty) {
    //     // 赋值拣货前的数量
    //     packedList.forEach(obj -> obj.setPackedBeforeQty(obj.getPackedQty()));
    //     checkAndAllocationQty(qty, () -> allocationQty(packedList, qty));
    // }

    /**
     * 校验并分配上架数量
     *
     * @param putAwayList 需要拣货的数据
     * @param qty         拣货数量
     */
    public static void checkAndAllocationPutAwayQty(List<? extends BasePutAwayVO> putAwayList, int qty) {
        // 赋值上架前的数量
        putAwayList.forEach(obj -> obj.setPutawayBeforeQty(obj.getPutawayQty()));
        checkAndAllocationQty(qty, () -> allocationQty(putAwayList, qty));
    }

    /**
     * 校验并分配上架数量
     *
     * @param putAwayList 需要拣货的数据
     * @param qty         拣货数量
     */
    public static void checkAndAllocationQty(List<? extends Allocation> putAwayList, int qty) {
        checkAndAllocationQty(qty, () -> allocationQty(putAwayList, qty));
    }

    /**
     * 校验并分配发货数量
     *
     * @param qty 拣货数量
     */
    public static void checkAndAllocationQty(int qty, Supplier<Integer> allocation) {
        if (qty < 0) {
            throw new BusinessException("Do not enter quantity?");
        }
        // 数量不够分配
        if (allocation.get() > 0) {
            throw new BusinessException("Exceeding the maximum allocation quantity");
        }
    }

    // /**
    //  * 分配拣货数量
    //  *
    //  * @param pickList 需要拣货的数据
    //  * @param qty      拣货数量
    //  * @return 剩余拣货数量未拣货
    //  */
    // public static int allocationPickQty(List<? extends BasePickVO> pickList, int qty) {
    //     int currentQty = qty;
    //     // 赋值拣货前的数量
    //     pickList.forEach(obj -> obj.setPickedBeforeQty(obj.getPickedQty()));
    //     // 拣货
    //     for (BasePickVO pick : pickList) {
    //         if (currentQty <= 0) {
    //             break;
    //         }
    //         // 需要拣货数量 - 当前拣货数量
    //         int pickedAfterQty = pick.getQty() - pick.getPickedQty() - currentQty;
    //         // 库存够
    //         if (pickedAfterQty >= 0) {
    //             // 设置拣货数量
    //             pick.setPickedQty(currentQty + pick.getPickedQty());
    //             currentQty = 0;
    //             // 不够扣，说明需要下一个元素继续扣减
    //         } else {
    //             // 剩下的数量
    //             currentQty = currentQty - pick.getQty() + pick.getPickedQty();
    //             // 直接拣货完
    //             pick.setPickedQty(pick.getQty());
    //         }
    //     }
    //     return currentQty;
    // }

    /**
     * 分配数量
     *
     * @param allocations 需要分配的数据
     * @param qty         需要分配的数量
     * @return 剩余数量未分配
     */
    public static int allocationQty(List<? extends Allocation> allocations, int qty) {
        int remainQty = qty;
        // 拣货
        for (Allocation allocation : allocations) {
            if (remainQty <= 0) {
                break;
            }
            // 当前可以分配的数量
            int currentAllocation = Math.min(allocation.total() - allocation.allocated(), remainQty);
            allocation.allocation(currentAllocation + allocation.allocated());
            remainQty -= currentAllocation;
        }
        return remainQty;
    }

    /**
     * 拆分列表
     *
     * @param splitList 需要拆分的列表
     * @param canSplit  是否可拆分函数
     */
    public static <Unit> int split(List<Unit> splitList, Predicate<List<Unit>> canSplit, Consumer<List<Unit>> allocationProcessor) {
        // 就一个元素不能在拆分
        if (ObjectUtil.isEmpty(splitList) || splitList.size() == 1) {
            return 0;
        }

        // 分配的拣货单详情集合
        List<Unit> allocation = new ArrayList<>();
        // 标记是否发生拆分
        boolean hasSplit = false;
        // 先把前面的分到一个拣货单，剩下没有分配的再递归走下面逻辑
        for (Unit detail : splitList) {
            // 先分配再计算
            allocation.add(detail);
            // 未发生拆分并才可以拆分
            if (!hasSplit && canSplit.test(allocation)) {
                // 标记发生拆分
                hasSplit = true;
                // 需要拆分，将分配完的移除
                allocation.remove(allocation.size() - 1);

                // 创建新的拣货单
                allocationProcessor.accept(allocation);
                // 清空
                allocation.clear();
                // 重新分配
                allocation.add(detail);
            }
        }
        return hasSplit
                // 发生拆分，将剩下的数据继续拆分
                ? split(allocation, canSplit, allocationProcessor) + 1
                : 0;
    }


    /**
     * 分配拣货数量通用逻辑封装
     *
     * @param pickingSlipPickList      拣货单拣货信息
     * @param buildPickProcessor       构建工单拣货处理骑
     * @param allocationDetailGroupMap 需要分配的详情集合
     * @return /
     */
    public static <PS extends ReadyToGoProductBinLocationPickVO, WP extends BasePickVO, WD extends ProductModel> List<WP> checkAndAllocationPickQty(List<PS> pickingSlipPickList,
                                                                                                                                                    Map<Long, List<WD>> allocationDetailGroupMap,
                                                                                                                                                    BiFunction<WD, PS, WP> buildPickProcessor) {
        // ProductBinLocationPickVO
        return checkAndAllocationPickQty(pickingSlipPickList, allocationDetailGroupMap, ProductBinLocationPickVO::getProductId, buildPickProcessor);
    }

    /**
     * 分配拣货数量通用逻辑封装
     *
     * @param pickingSlipPickList      拣货单拣货信息
     * @param groupKey                 分组拣货的key
     * @param buildPickProcessor       构建工单拣货处理骑
     * @param allocationDetailGroupMap 需要分配的详情集合
     * @return /
     */
    public static <PS extends ReadyToGoProductBinLocationPickVO, WP extends BasePickVO, WD extends ProductModel, Key> List<WP> checkAndAllocationPickQty(
            List<PS> pickingSlipPickList, Map<Key, List<WD>> allocationDetailGroupMap, Function<PS, Key> groupKey, BiFunction<WD, PS, WP> buildPickProcessor) {

        // 拣货单分配拣货数量逻辑处理
        return pickingSlipPickList.stream()
                // 发生拣货的拣货信息
                .filter(pick -> pick.getChangePickQty() > 0)
                .filter(pick -> allocationDetailGroupMap.containsKey(groupKey.apply(pick)))
                .flatMap(pick -> {
                    List<WP> details = allocationDetailGroupMap.get(groupKey.apply(pick))
                            .stream()
                            .map(detail -> {
                                WP detailPick = buildPickProcessor.apply(detail, pick);
                                // 设置拣货之前的数量
                                detailPick.setPickedBeforeQty(detailPick.getPickedQty());
                                return detailPick;
                            })
                            .toList();
                    int remainingQty = allocationQty(details, pick.getChangePickQty());
                    if (remainingQty == 0) {
                        Map<Long, WD> detailMap = StreamUtils.toMap(allocationDetailGroupMap.get(groupKey.apply(pick)), IdModel::getId);
                        // 设置 Detail 更新后的拣货数量
                        details.stream()
                                .filter(obj -> obj.getChangePickQty() > 0)
                                .forEach(obj -> detailMap.get(obj.getId()).setPickedQty(obj.getPickedQty()));

                        return details.stream();
                    }
                    // 剩余数量大于0 不够分配
                    String message = StringUtil.format("PickingSlip: {} Failed to allocate Product {}, remainingPickQty: {}",
                            pick.getRefTableShowName(),
                            Optional.ofNullable(ProductCacheUtil.getById(pick.getProductId()))
                                    .map(ProductCache::getSupplierSku)
                                    .orElse(StringPool.EMPTY),
                            remainingQty
                    );
                    throw new BusinessException(message);
                })
                .filter(obj -> obj.getChangePickQty() > 0)
                .toList();
    }
}
