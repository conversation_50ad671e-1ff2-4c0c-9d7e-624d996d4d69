package cn.need.cloud.biz.util;

import cn.need.cloud.biz.cache.ProductCacheRepertory;
import cn.need.cloud.biz.cache.ProductCacheService;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.util.SpringUtil;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
public class ProductCacheUtil {

    /**
     * 取值，赋值方法缓存
     */
    private static final Map<String, Method> METHOD_CACHE = Maps.newConcurrentMap();
    /**
     * 不存在产品方法的缓存key集合
     */
    private static final Set<String> NOT_EXISTS_CACHE_KEYS = new HashSet<>();
    /**
     * 产品id
     */
    private static final String PRODUCT_ID = "productId";
    /**
     * 产品id
     */
    private static final Class<BaseProductVO> BASE_PRODUCT_VO = BaseProductVO.class;
    /**
     * 产品缓存实现类
     */
    private static ProductCacheService productCacheService;
    /**
     * 用来缓存系统配置的工具对象
     */
    private static ProductCacheRepertory repertory;

    private ProductCacheUtil() {
        throw new AssertionError("No " + getClass().getName() + " instances for you!");
    }

    /**
     * 根据产品id，获取产品缓存
     *
     * @param id 产品id
     * @return ProductCache 产品缓存
     */
    public static ProductCache getById(Long id) {
        //获取产品
        ProductService productService = SpringUtil.getBean(ProductService.class);
        //判空
        Validate.notNull(productService, "productService can not null");
        //从缓存中获取数据
        ProductCache productCache = getCacheService().getById(id);
        //缓存中数据为空则从数据库中获取
        if (ObjectUtil.isEmpty(productCache)) {
            boolean ignore = WarehouseContextHolder.isIgnore();
            try {
                productCache = BeanUtil.copyNew(productService.getById(id), ProductCache.class);
                // 添加缓存
                ProductCacheRepertory cacheRepertory = getRepertory();
                cacheRepertory.addProduct(productCache);
            } catch (Exception e) {
                // 处理异常
                e.printStackTrace();
            } finally {
                WarehouseContextHolder.setIgnore(ignore);
            }
        }
        return productCache;
    }

    /**
     * 根据产品id集合，获取产品缓存集合
     *
     * @param ids 产品id集合
     * @return List<ProductCache> 产品缓存集合
     */
    public static List<ProductCache> listByIds(Collection<Long> ids) {
        //获取产品
        ProductService productService = SpringUtil.getBean(ProductService.class);
        //判空
        Validate.notNull(productService, "productService can not null");
        //从缓存中获取数据
        List<ProductCache> productCaches = getCacheService().listByIds(ids).stream().filter(ObjectUtil::isNotNull).toList();
        //缓存中数据为空则从数据库中获取
        if (ObjectUtil.isEmpty(productCaches) || ids.size() != productCaches.size() || productCaches.get(0) == null) {
            boolean ignore = WarehouseContextHolder.isIgnore();
            WarehouseContextHolder.setIgnore(Boolean.TRUE);
            try {
                List<Product> productList = productService.listByIds(ids);
                productCaches = BeanUtil.copyNew(productList, ProductCache.class);
                // 添加缓存
                ProductCacheRepertory cacheRepertory = getRepertory();
                cacheRepertory.addProduct(productCaches);
            } catch (Exception e) {
                // 处理异常
                e.printStackTrace();
            } finally {
                WarehouseContextHolder.setIgnore(ignore);
            }
        }
        return productCaches;
    }

    /**
     * 填充数据对象中的产品信息，接收一个数据对象
     * 填充规则：
     * 1. 会从ProductId字段中获取用户的id值，获取名字后，填充到baseProductVO字段中
     *
     * @param bean 需要填充的数据对象
     */
    public static <T> T filledProductWithReturn(T bean) {
        filledProduct(bean);
        return bean;
    }

    /**
     * 填充数据对象中的产品信息，接收一个数据对象
     * 填充规则：
     * 1. 会从ProductId字段中获取用户的id值，获取名字后，填充到baseProductVO字段中
     *
     * @param bean 需要填充的数据对象
     */
    public static <T> void filledProduct(T bean) {
        if (isNotNull(bean)) {
            filledProduct(Lists.newArrayList(bean));
        }
    }

    /**
     * 填充数据对象中的产品信息，接收一个数据对象
     * 填充规则：
     * 1. 会从ProductId字段中获取用户的id值，获取名字后，填充到baseProductVO字段中
     *
     * @param beans 需要填充的数据对象集合
     */
    public static <T> Collection<T> filledProductWithReturn(Collection<T> beans) {
        filledProduct(beans);
        return beans;
    }

    /**
     * 填充数据对象中的产品信息，接收一个数据对象
     * 填充规则：
     * 1. 会从ProductId字段中获取用户的id值，获取名字后，填充到baseProductVO字段中
     *
     * @param beans 需要填充的数据对象集合
     */
    public static <T> void filledProduct(Collection<T> beans) {
        //1. 从bean集合中收集产品id集合
        Set<Long> ids = getProductIds(beans);
        //2. 根据产品id集合，获取产品缓存信息，并封装成map
        Map<Long, ProductCache> cacheMap = toMap(listByIds(ids), ProductCache::getId);
        //3. 产品缓存集合不为空的情况下，填充产品对象
        if (!cacheMap.isEmpty()) {
            beans.forEach(it -> {
                //4. 填充产品对象
                Long productId = getProductId(it);
                log.debug("=============================>ProductId{}", productId);
                if (isNotNull(productId)) {
                    setBaseProduct(it, cacheMap.get(productId));
                }
            });
        }
    }


    /**
     * bean对象集合中，获取创建人、更新人id集合
     */
    private static <T> Set<Long> getProductIds(Collection<T> beans) {
        Set<Long> sets = new HashSet<>();
        if (isNotEmpty(beans)) {
            beans.forEach(it -> {
                Long productId = getProductId(it);
                if (isNotNull(productId)) {
                    sets.add(productId);
                }
            });
        }
        return sets;
    }

    /**
     * spring容器中获取系统配置工具对象
     */
    private static ProductCacheRepertory getRepertory() {
        if (isNotNull(repertory)) {
            return repertory;
        }
        repertory = SpringUtil.getBean(ProductCacheRepertory.class);
        Validate.notNull(repertory, "Cannot get ProductCacheRepertory object from spring container!");
        return repertory;
    }

    /**
     * spring容器中获取系统配置工具对象
     */
    private static ProductCacheService getCacheService() {
        if (isNotNull(productCacheService)) {
            return productCacheService;
        }
        productCacheService = SpringUtil.getBean(ProductCacheService.class);
        Validate.notNull(productCacheService, "Cannot get ProductCacheService object from spring container!");
        return productCacheService;
    }

    /**
     * 数据对象中，获取创建人id字段值
     *
     * @param bean 数据对象
     * @return Long 创建人id
     */
    public static Long getProductId(Object bean) {
        return isNotNull(bean) ? getProperty(bean, PRODUCT_ID) : null;
    }

    /**
     * 获取属性字段值
     */
    private static Long getProperty(Object bean, String fieldName) {
        try {
            Method method = getMethod(bean.getClass(), fieldName);
            return isNotNull(method) ? ObjectUtil.convert(method.invoke(bean), null) : null;
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("获取bean的属性信息异常！bean={}{}{}{}{}", bean, " ,property", fieldName, ", 异常：", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据java类型，字段名称，获取字段映射的方法
     */
    private static Method getMethod(@NonNull Class<?> clazz, String fieldName) {
        //1. 优先根据类名称，方法名称，从缓存中获取已存在的方法
        String cacheKey = clazz.getName().concat(StringPool.HASH).concat(fieldName);
        //2. 缓存数据不存在的情况下，从类型中获取映射的方法后缓存
        if (!METHOD_CACHE.containsKey(cacheKey) && !NOT_EXISTS_CACHE_KEYS.contains(cacheKey)) {
            Method method = isRead(fieldName) ? BeanUtil.getReadMethod(clazz, fieldName) : BeanUtil.getWriteMethod(clazz, fieldName);
            if (isNotNull(method)) {
                METHOD_CACHE.put(cacheKey, method);
            } else {
                NOT_EXISTS_CACHE_KEYS.add(cacheKey);
                log.warn("不能从java类[{}]中获取到属性[{}]的赋值方法！", clazz.getName(), fieldName);
            }
        }
        //3. 从缓存中获取
        return METHOD_CACHE.get(cacheKey);
    }

    /**
     * 根据字段名称，判断是否获取只读方法
     */
    private static boolean isRead(String fieldName) {
        return equal(fieldName, PRODUCT_ID);
    }

    /**
     * 设置产品信息
     *
     * @param bean  需要设置的数据对象
     * @param cache 产品缓存
     */
    public static void setBaseProduct(Object bean, ProductCache cache) {
        log.debug("===========================>cache{}", cache);
        if (isNotNull(bean) && isNotNull(cache)) {
            List<Field> fields = BeanUtil.getFields(bean);
            for (Field item : fields) {
                Class<?> type = item.getType();
                if (BASE_PRODUCT_VO.isAssignableFrom(type)) {
                    // vo赋值
                    Object o = BeanUtil.copyNew(cache, type);
                    BaseProductVO vo = (BaseProductVO) o;
                    TenantCacheService service = SpringUtil.getBean(TenantCacheService.class);
                    Validate.notNull(service, "TenantCacheService can not null");
                    // // 设置供应商信息
                    // if (isNotNull(service.getById(vo.getTransactionPartnerId()))) {
                    //     vo.setTransactionPartner(BeanUtil.copyNew(service.getById(vo.getTransactionPartnerId()), BasePartnerVO.class));
                    // }
                    // 将vo 赋值到bean中
                    setProperty(bean, item.getName(), vo);
                    return;
                }
            }

        }
    }

    /**
     * 设置属性字段值
     */
    public static void setProperty(Object bean, String fieldName, Object value) {
        try {
            Method method = getMethod(bean.getClass(), fieldName);
            if (isNotNull(method)) {
                method.invoke(bean, value);
            }
        } catch (Exception e) {
            throw new RuntimeException("设置数据的属性值异常：" + e.getMessage(), e);
        }
    }
}
