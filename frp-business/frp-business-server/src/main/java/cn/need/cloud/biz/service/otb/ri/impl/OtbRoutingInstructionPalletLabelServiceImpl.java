package cn.need.cloud.biz.service.otb.ri.impl;

import cn.need.cloud.biz.converter.otb.OtbRoutingInstructionPalletLabelConverter;
import cn.need.cloud.biz.mapper.otb.OtbRoutingInstructionPalletLabelMapper;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstructionPalletLabel;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionCreateParam;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionPalletLabelCreateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionPalletLabelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionUpdateParam;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionPalletLabelQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPalletLabelPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionPalletLabelVO;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionPalletLabelService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * otb发货指南托盘标签 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbRoutingInstructionPalletLabelServiceImpl extends SuperServiceImpl<OtbRoutingInstructionPalletLabelMapper, OtbRoutingInstructionPalletLabel> implements OtbRoutingInstructionPalletLabelService {

    @Resource
    private FileStringUploadService fileStringUploadService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(OtbRoutingInstructionPalletLabelCreateParam createParam) {
        // 检查传入otb发货指南托盘标签参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取otb发货指南托盘标签转换器实例，用于将otb发货指南托盘标签参数对象转换为实体对象
        OtbRoutingInstructionPalletLabelConverter converter = Converters.get(OtbRoutingInstructionPalletLabelConverter.class);

        // 将otb发货指南托盘标签参数对象转换为实体对象并初始化
        OtbRoutingInstructionPalletLabel entity = initOtbRoutingInstructionPalletLabel(converter.toEntity(createParam));

        // 插入otb发货指南托盘标签实体对象到数据库
        super.insert(entity);

        // 返回otb发货指南托盘标签ID
        return entity.getId();
    }

    @Override
    public void insertBatchByParam(OtbRoutingInstructionCreateParam param, long otbRoutingInstructionId) {
        List<OtbRoutingInstructionPalletLabelVO> palletList = param.getRoutingInstructionPalletLabelList();
        // 校验参数是否合法
        checkParamValid(palletList);

        // 获取OTB请求详情转换器实例，用于将OTB请求详情参数对象转换为实体对象
        OtbRoutingInstructionPalletLabelConverter converter = Converters.get(OtbRoutingInstructionPalletLabelConverter.class);

        List<OtbRoutingInstructionPalletLabel> detailEntities = converter.toEntity(palletList);

        detailEntities.forEach(o -> {
            o.setOtbRoutingInstructionId(otbRoutingInstructionId);
            //设置id 方便文件上传映射填充
            o.setId(IdWorker.getId());
        });
        fileStringUploadService.uploadLabelBatch(detailEntities);
        //批量新增
        super.insertBatch(detailEntities);

    }

    /**
     * 校验参数是否合法
     */
    private void checkParamValid(List<OtbRoutingInstructionPalletLabelVO> palletList) {
        long count = palletList.stream().map(OtbRoutingInstructionPalletLabelVO::getLineNum).distinct().count();
        if (count != palletList.size()) {
            throw new RuntimeException("PalletLabelLineNum must be unique");
        }
    }

    @Override
    public void updateBatchByParam(OtbRoutingInstructionUpdateParam updateParam) {
        List<OtbRoutingInstructionPalletLabelVO> palletLabelList = updateParam.getRoutingInstructionPalletLabelList();
        //校验参数有效性
        checkParamValid(palletLabelList);

        List<OtbRoutingInstructionPalletLabel> oldList = getListByRoutingInstructionId(updateParam.getId());

        //根据lineNum映射 oldId
        Map<Integer, Long> oldMap = oldList.stream().collect(Collectors.toMap(OtbRoutingInstructionPalletLabel::getLineNum, OtbRoutingInstructionPalletLabel::getId));

        // 获取OTB请求详情转换器实例，用于将OTB请求详情参数对象转换为实体对象
        OtbRoutingInstructionPalletLabelConverter converter = Converters.get(OtbRoutingInstructionPalletLabelConverter.class);

        List<OtbRoutingInstructionPalletLabel> newList = converter.toEntity(palletLabelList);
        //根据lineNum映射 新对象
        Map<Integer, OtbRoutingInstructionPalletLabel> newMap = newList.stream().collect(Collectors.toMap(OtbRoutingInstructionPalletLabel::getLineNum, o -> o));
        //取lineNum 交集
        Set<Integer> intersection = new HashSet<>(oldMap.keySet());
        intersection.retainAll(newMap.keySet());
        //批量更新
        List<OtbRoutingInstructionPalletLabel> updateList = newList.stream().filter(o -> intersection.contains(o.getLineNum())).toList();
        updateList.forEach(o -> o.setId(oldMap.get(o.getLineNum())));
        // 创建集合收集要上传的文件
        ArrayList<OtbRoutingInstructionPalletLabel> uploadLabelList = new ArrayList<>(updateList);
        //创建一个集合收集要删除的数据
        ArrayList<Long> deleteIdList = new ArrayList<>();
        //创建一个集合收集要新增的数据
        ArrayList<OtbRoutingInstructionPalletLabel> insertList = new ArrayList<>();

        // 需要删除的数据的lineNum
        Set<Integer> delList = new HashSet<>(oldMap.keySet());
        delList.removeAll(newMap.keySet());
        if (ObjectUtil.isNotEmpty(delList)) {
            List<Long> idList = oldList.stream().filter(o -> delList.contains(o.getLineNum())).map(OtbRoutingInstructionPalletLabel::getId).toList();
            //批量删除
            deleteIdList.addAll(idList);
        }
        // 需要新增的数据的lineNum
        Set<Integer> addList = new HashSet<>(newMap.keySet());
        addList.removeAll(oldMap.keySet());
        if (ObjectUtil.isNotEmpty(addList)) {
            List<OtbRoutingInstructionPalletLabel> list = newList.stream().filter(o -> addList.contains(o.getLineNum())).toList();
            list.forEach(o -> {
                o.setOtbRoutingInstructionId(updateParam.getId());
                //设置id 方便文件上传映射填充
                o.setId(IdWorker.getId());
            });
            //收集要上传的文件
            uploadLabelList.addAll(list);
            //批量新增
            insertList.addAll(list);
        }
        //批量上传文件
        fileStringUploadService.uploadLabelBatch(uploadLabelList);
        // 批量修改
        super.updateBatch(updateList);
        // 批量删除
        super.removeByIds(deleteIdList);
        // 批量新增
        super.insertBatch(insertList);
    }

    @Override
    public List<OtbRoutingInstructionPalletLabel> getListByRoutingInstructionId(Long routingInstructionId) {
        return lambdaQuery().eq(OtbRoutingInstructionPalletLabel::getOtbRoutingInstructionId, routingInstructionId).list();
    }


    /**
     * 初始化otb发货指南托盘标签对象
     * 此方法用于设置otb发货指南托盘标签对象的必要参数，确保其处于有效状态
     *
     * @param entity otb发货指南托盘标签对象，不应为空
     * @return 返回初始化后的otb发货指南托盘标签
     * @throws BusinessException 如果传入的otb发货指南托盘标签为空，则抛出此异常
     */
    private OtbRoutingInstructionPalletLabel initOtbRoutingInstructionPalletLabel(OtbRoutingInstructionPalletLabel entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OtbRoutingInstructionPalletLabel cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(OtbRoutingInstructionPalletLabelUpdateParam updateParam) {
        // 检查传入otb发货指南托盘标签参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取otb发货指南托盘标签转换器实例，用于将otb发货指南托盘标签参数对象转换为实体对象
        OtbRoutingInstructionPalletLabelConverter converter = Converters.get(OtbRoutingInstructionPalletLabelConverter.class);

        // 将otb发货指南托盘标签参数对象转换为实体对象
        OtbRoutingInstructionPalletLabel entity = converter.toEntity(updateParam);

        // 执行更新otb发货指南托盘标签操作
        return super.update(entity);

    }

    @Override
    public List<OtbRoutingInstructionPalletLabelPageVO> listByQuery(OtbRoutingInstructionPalletLabelQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtbRoutingInstructionPalletLabelPageVO> pageByQuery(PageSearch<OtbRoutingInstructionPalletLabelQuery> search) {
        Page<OtbRoutingInstructionPalletLabel> page = Conditions.page(search, entityClass);
        List<OtbRoutingInstructionPalletLabelPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbRoutingInstructionPalletLabelVO detailById(Long id) {
        OtbRoutingInstructionPalletLabel entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtbRoutingInstructionPalletLabel");
        }
        return buildOtbRoutingInstructionPalletLabelVO(entity);
    }


    /**
     * 构建otb发货指南托盘标签VO对象
     *
     * @param entity otb发货指南托盘标签对象
     * @return 返回包含详细信息的otb发货指南托盘标签VO对象
     */
    private OtbRoutingInstructionPalletLabelVO buildOtbRoutingInstructionPalletLabelVO(OtbRoutingInstructionPalletLabel entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的otb发货指南托盘标签VO对象
        return Converters.get(OtbRoutingInstructionPalletLabelConverter.class).toVO(entity);
    }

}
