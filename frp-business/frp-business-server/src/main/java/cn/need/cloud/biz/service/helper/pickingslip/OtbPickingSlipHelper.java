package cn.need.cloud.biz.service.helper.pickingslip;

import cn.need.cloud.biz.client.constant.enums.otb.OtbPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlip;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlipDetail;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.mybatis.model.IdModel;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * PickingSlipHelper
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public class OtbPickingSlipHelper {

    /**
     * 重新刷新拣货单状态
     *
     * @param prepPickingSlips 拣货单
     * @param detailGroupMap   详情
     */
    public static void refreshStatus(List<OtbPickingSlip> prepPickingSlips, Map<Long, List<OtbPickingSlipDetail>> detailGroupMap) {
        var prepPickingSlipMap = StreamUtils.toMap(prepPickingSlips, IdModel::getId);
        // 拣货单状态更新
        detailGroupMap.forEach((prepPickingSlipId, details) -> {
            var prepPickingSlip = prepPickingSlipMap.get(prepPickingSlipId);

            // 全部上架 ReadyToShip
            var allPacked = details.stream().allMatch(obj -> Objects.equals(obj.getPackedQty(), obj.getQty()));
            // 全部拣货 Picked
            var allPicked = details.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
            // 拣货 InPicking
            var hasPick = details.stream().anyMatch(obj -> obj.getPickedQty() > 0);

            var oldStatus = prepPickingSlip.getOtbPickingSlipStatus();
            prepPickingSlip.setOtbPickingSlipStatus(allPacked ? OtbPickingSlipStatusEnum.PACKED.getStatus()
                    : allPicked ? OtbPickingSlipStatusEnum.PICKED.getStatus()
                    : hasPick ? OtbPickingSlipStatusEnum.IN_PICKING.getStatus()
                    : OtbPickingSlipStatusEnum.NEW.getStatus()
            );

            // 记录日志
            if (!Objects.equals(oldStatus, prepPickingSlip.getOtbPickingSlipStatus())) {
                OtbPickingSlipAuditLogHelper.recordLog(prepPickingSlip);
            }
        });
    }

    /**
     * 重新刷新拣货单状态
     *
     * @param prepPickingSlips 拣货单
     * @param detailGroupMap   详情
     */
    public static void refreshPrepStatus(List<OtbPrepPickingSlip> prepPickingSlips, Map<Long, List<OtbPrepPickingSlipDetail>> detailGroupMap) {
        var prepPickingSlipMap = StreamUtils.toMap(prepPickingSlips, IdModel::getId);
        // 拣货单状态更新
        detailGroupMap.forEach((prepPickingSlipId, details) -> {
            var prepPickingSlip = prepPickingSlipMap.get(prepPickingSlipId);

            // 全部上架 Putaway
            var allPutaway = details.stream().allMatch(obj -> Objects.equals(obj.getPutawayQty(), obj.getQty()));
            // 全部拣货 Picked
            var allPicked = details.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
            // 拣货 InPicking
            var hasPick = details.stream().anyMatch(obj -> obj.getPickedQty() > 0);

            var oldStatus = prepPickingSlip.getOtbPrepPickingSlipStatus();
            prepPickingSlip.setOtbPrepPickingSlipStatus(allPutaway ? OtbPrepPickingSlipStatusEnum.PUT_AWAY.getStatus()
                    : allPicked ? OtbPrepPickingSlipStatusEnum.PICKED.getStatus()
                    : hasPick ? OtbPrepPickingSlipStatusEnum.IN_PICKING.getStatus()
                    : OtbPrepPickingSlipStatusEnum.NEW.getStatus()
            );

            // 记录日志
            if (!Objects.equals(oldStatus, prepPickingSlip.getOtbPrepPickingSlipStatus())) {
                OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip);
            }
        });
    }
}
