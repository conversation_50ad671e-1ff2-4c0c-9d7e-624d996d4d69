package cn.need.cloud.biz.model.vo.base.pickingslip;

import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductVersionAware;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC预拣货单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "拣货单 Unpick详情 vo对象")
public class BasePickingSlipUnpickDetailVO implements Serializable, BaseFullProductVersionAware, BaseBinLocationAware {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    @JsonIgnore
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 库位id
     */
    @Schema(description = "库位详情id")
    @JsonIgnore
    private Long binLocationDetailId;

    // ------------------------- 工单详情 -------------------------

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 拣货 数量
     */
    @Schema(description = "拣货 数量")
    private Integer pickedQty;

    public Integer getPickedQty() {
        return ObjectUtil.nullToDefault(pickedQty, 0);
    }

    @Schema(description = "可 Rollback 数量")
    public Integer getCanRollbackQty() {
        return this.getPickedQty();
    }

    @Schema(description = "unpick 主键")
    public Long getUnpickId() {
        return 0L;
    }
}