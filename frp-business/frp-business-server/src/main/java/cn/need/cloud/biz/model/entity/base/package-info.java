/**
 * 基础实体模型包，提供系统核心业务对象的数据结构定义。
 * 
 * <p>该包包含业务模块的基础实体模型，主要定义了请求、工单、标签等基础数据结构。
 * 这些实体是系统数据持久化的核心对象，直接映射到数据库表结构。
 * 基础实体继承了通用属性（如ID、创建时间、更新时间等），并添加了业务特定的属性。
 * </p>
 * 
 * <p>主要实体包括：
 * <ul>
 *   <li>请求实体 - 定义客户和供应商的请求数据结构</li>
 *   <li>工单实体 - 定义系统中各类工单的数据结构</li>
 *   <li>标签实体 - 定义系统中使用的标签数据结构</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 * @version 1.0.0
 */
package cn.need.cloud.biz.model.entity.base;