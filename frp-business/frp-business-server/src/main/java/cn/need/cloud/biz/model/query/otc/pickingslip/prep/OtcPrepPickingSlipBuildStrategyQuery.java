package cn.need.cloud.biz.model.query.otc.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


/**
 * OTC工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC工单 FilterBuildPickingSlip 构建查询Query - strategy query对象")
public class OtcPrepPickingSlipBuildStrategyQuery {

    /**
     * 一次拣货单上最多 拥有的 Prep工单数量
     */
    @Schema(description = "一次拣货单上最多 拥有的 Prep工单数量")
    @Min(value = 1, message = "MaxCanBuildSum must be greater than or equal to 1")
    @NotNull(message = "MaxCanBuildSum must is not null")
    private Integer maxPrepWorkOrderCount;
}