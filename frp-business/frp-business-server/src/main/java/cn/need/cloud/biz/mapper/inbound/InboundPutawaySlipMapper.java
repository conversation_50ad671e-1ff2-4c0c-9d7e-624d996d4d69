package cn.need.cloud.biz.mapper.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlip;
import cn.need.cloud.biz.model.query.inbound.InboundPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.page.InboundPutawaySlipPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 上架 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InboundPutawaySlipMapper extends SuperMapper<InboundPutawaySlip> {

    /**
     * 根据条件获取上架列表
     *
     * @param query 查询条件
     * @return 上架集合
     */
    default List<InboundPutawaySlipPageVO> listByQuery(InboundPutawaySlipQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取上架分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 上架集合
     */
    List<InboundPutawaySlipPageVO> listByQuery(@Param("qo") InboundPutawaySlipQuery query, @Param("page") Page<?> page);
}