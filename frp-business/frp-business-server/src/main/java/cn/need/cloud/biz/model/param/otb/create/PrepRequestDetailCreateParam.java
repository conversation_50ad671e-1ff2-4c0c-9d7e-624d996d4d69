package cn.need.cloud.biz.model.param.otb.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 预请求详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "预请求详情 vo对象")
public class PrepRequestDetailCreateParam implements Serializable {


    /**
     * 准备请求ID
     */
    @Schema(description = "准备请求ID")
    private Long prepRequestId;

    /**
     * 准备类型
     */
    @Schema(description = "准备类型")
    private String prepType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

}