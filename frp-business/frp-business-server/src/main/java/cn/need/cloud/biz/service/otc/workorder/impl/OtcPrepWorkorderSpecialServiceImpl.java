package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.special.RollbackConstant;
import cn.need.cloud.biz.model.bo.base.ChangeQtyLogBO;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcPrepWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcPrepWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseReservedParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelContext;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.PrepPickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderFinishConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmVO;
import cn.need.cloud.biz.service.helper.LockedHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.workorder.OtcWorkorderHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipDetailService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipService;
import cn.need.cloud.biz.service.otc.workorder.*;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC预提工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Lazy))
public class OtcPrepWorkorderSpecialServiceImpl implements OtcPrepWorkorderSpecialService {

    // ======================================== 常量定义 ========================================

    private static final String OPERATION_FAILED_UPDATE_PREP_WORKORDER = "update PrepWorkOrder status failed";
    private static final String OPERATION_FAILED_UPDATE_PREP_WORKORDER_DETAIL = "update PrepWorkOrderDetail status failed";
    private static final String OPERATION_FAILED_UPDATE_INVENTORY_LOCKED = "update InventoryLocked failed";

    // ======================================== 依赖注入 ========================================

    private final OtcWorkorderService otcWorkorderService;
    private final OtcPrepWorkorderService otcPrepWorkorderService;
    private final OtcPrepPickingSlipSpecialService otcPrepPickingSlipSpecialService;
    private final OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;
    private final OtcPrepPutawaySlipService otcPrepPutawaySlipService;
    private final OtcPrepPutawaySlipDetailService otcPrepPutawaySlipDetailService;
    private final OtcPrepWorkorderBinLocationService otcPrepWorkorderBinLocationService;
    private final InventoryLockedService inventoryLockedService;
    private final OtcPrepPickingSlipService otcPrepPickingSlipService;
    private final InventoryReserveService inventoryReserveService;
    private final OtcPrepPickingSlipDetailService otcPrepPickingSlipDetailService;

    // ======================================== 公共方法 ========================================

    /**
     * 单个PrepWorkorder拆单
     *
     * @param splitHolder           拆单
     * @param prep                  prep工单
     * @param prepWkDetailsGroupMap Details
     * @return /
     */
    private static OtcPrepWorkorderSplitBO splitSingle(OtcWorkorderSplitDetailBO splitHolder,
                                                       OtcPrepWorkorder prep,
                                                       Map<Long, List<OtcPrepWorkorderDetail>> prepWkDetailsGroupMap) {
        prep.setQty(prep.getQty() - splitHolder.getSplitQty());
        // 上架数量, 尽量留在原单中
        var splitPutAwayQty = prep.getQty() > prep.getPutawayQty()
                ? 0 : prep.getPutawayQty() - prep.getQty();
        prep.setPutawayQty(prep.getPutawayQty() - splitPutAwayQty);

        // 拆单
        var splitPrepWk = BeanUtil.copyNew(prep, OtcPrepWorkorder.class);
        splitPrepWk.setId(IdWorker.getId());
        splitPrepWk.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PREP_WORK_ORDER.getCode()));
        splitPrepWk.setQty(splitHolder.getSplitQty());
        splitPrepWk.setPutawayQty(splitPutAwayQty);
        // 赋值工单信息
        splitPrepWk.setOtcWorkorderId(splitHolder.getSplitDetail().getOtcWorkorderId());
        splitPrepWk.setOtcWorkorderDetailId(splitHolder.getSplitDetail().getId());

        // Details 拆
        var splitDetails = prepWkDetailsGroupMap.getOrDefault(prep.getId(), Collections.emptyList()).stream()
                .map(prepDetail -> {
                    var headerQty = prep.getQty() + splitHolder.getSplitQty();
                    // Child.SplitQty = ChildQty * SplitQty / HeaderQty
                    var childSplitQty = prepDetail.getQty() * splitHolder.getSplitQty() / headerQty;
                    // 原来的PrepDetail更新数量
                    prepDetail.setQty(prepDetail.getQty() - childSplitQty);
                    // Child.PutawayQty
                    var splitChildPutAwayQty = prepDetail.getQty() > prepDetail.getPutawayQty()
                            ? 0 : prepDetail.getPutawayQty() - prepDetail.getQty();
                    prepDetail.setPutawayQty(prepDetail.getPutawayQty() - splitChildPutAwayQty);
                    // Child.PickedQty
                    var splitChildPickedQty = prepDetail.getQty() > prepDetail.getPickedQty()
                            ? 0 : prepDetail.getPickedQty() - prepDetail.getQty();
                    prepDetail.setPickedQty(prepDetail.getPickedQty() - splitChildPickedQty);

                    // 拆单
                    var splitPrepWkDetail = BeanUtil.copyNew(prepDetail, OtcPrepWorkorderDetail.class);
                    splitPrepWkDetail.setId(IdWorker.getId());
                    // 设置HeaderId
                    splitPrepWkDetail.setOtcPrepWorkorderId(splitPrepWk.getId());
                    splitPrepWkDetail.setQty(childSplitQty);
                    splitPrepWkDetail.setPutawayQty(splitChildPutAwayQty);
                    splitPrepWkDetail.setPickedQty(splitChildPickedQty);

                    var prepDetailSplitHolder = new OtcPrepWorkorderSplitDetailBO();
                    prepDetailSplitHolder.setSplitQty(childSplitQty);
                    prepDetailSplitHolder.setPrepDetail(prepDetail);
                    prepDetailSplitHolder.setSplitPrepDetail(splitPrepWkDetail);
                    return prepDetailSplitHolder;
                })
                .toList();

        // 封装结果
        var prepSplitHolder = new OtcPrepWorkorderSplitBO();
        prepSplitHolder.setPrepWorkorder(prep);
        prepSplitHolder.setSplitPrepWorkorder(splitPrepWk);
        prepSplitHolder.setPrepDetailHolders(splitDetails);
        prepSplitHolder.setSplitQty(splitHolder.getSplitQty());

        return prepSplitHolder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processTriggering(WorkorderProcessBO process) {
        if (ObjectUtil.isEmpty(process.getHasPrepWorkorderIds())) {
            return;
        }

        ProcessType processType = process.getProcessType();
        switch (processType) {
            case ROLLBACKING, CANCELLING -> {
                List<OtcPrepWorkorder> workorderList = this.getAndCheckStartWorkorder(process);
                process.setPrepWorkorderList(workorderList);
                this.processTriggering(process, workorderList);
            }
            case NORMAL -> {
                List<OtcPrepWorkorder> workorderList = this.getAndCheckFinishWorkorder(process);
                process.setPrepWorkorderList(workorderList);
                this.processTriggering(process, workorderList);
            }
        }
    }

    @Override
    public void rollback(OtcPrepPutawaySlipPutAwayBO putawayParam) {
        OtcPrepPutawaySlip putawaySlip = putawayParam.getPutawaySlip();

        // 获取工单和明细信息
        List<OtcPrepWorkorderDetail> wkDetails = otcPrepWorkorderDetailService.listByPrepWorkOrderId(putawaySlip.getPrepWorkorderId());
        OtcPrepWorkorder workorder = otcPrepWorkorderService.getById(putawaySlip.getPrepWorkorderId());
        Map<Long, OtcPrepWorkorderDetail> detailMap = StreamUtils.toMap(wkDetails, IdModel::getId);

        // 执行回滚操作
        List<ChangeQtyLogBO> changeList = processRollbackDetails(putawayParam, detailMap);

        // 更新工单状态
        updateWorkorderStatusForRollback(workorder, putawayParam.getNote());

        // 更新工单明细
        updateRollbackDetails(putawayParam, changeList, workorder);
    }

    /**
     * 处理回滚明细
     *
     * @param putawayParam 上架参数
     * @param detailMap    明细映射
     * @return 变更日志列表
     */
    private List<ChangeQtyLogBO> processRollbackDetails(OtcPrepPutawaySlipPutAwayBO putawayParam,
                                                        Map<Long, OtcPrepWorkorderDetail> detailMap) {
        return putawayParam.getDetailList().stream()
                .map(paramDetail -> {
                    OtcPrepPutawaySlipDetail putawaySlipDetail = paramDetail.getPutawaySlipDetail();
                    OtcPrepWorkorderDetail wkDetail = detailMap.get(putawaySlipDetail.getPrepWorkorderDetailId());

                    // 回滚拣货数量
                    int beforeQty = wkDetail.getPickedQty();
                    wkDetail.setPickedQty(beforeQty - paramDetail.getPutawayQty());

                    // 创建变更日志
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(beforeQty);
                    change.setAfterQty(wkDetail.getPickedQty());
                    change.setProductId(wkDetail.getProductId());

                    paramDetail.setWorkorderDetail(wkDetail);
                    return change;
                })
                .toList();
    }

    /**
     * 更新工单状态为回滚状态
     *
     * @param workorder 工单
     * @param note      备注
     */
    private void updateWorkorderStatusForRollback(OtcPrepWorkorder workorder, String note) {
        String oldStatus = workorder.getPrepWorkorderStatus();
        workorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.IN_PICKING.getStatus());

        if (!Objects.equals(oldStatus, workorder.getPrepWorkorderStatus())) {
            Validate.isTrue(otcPrepWorkorderService.update(workorder) == 1,
                    String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrder status"));
            OtcPrepWorkorderAuditLogHelper.recordLog(workorder, null, note);
        }
    }

    /**
     * 更新回滚明细
     *
     * @param putawayParam 上架参数
     * @param changeList   变更列表
     * @param workorder    工单
     */
    private void updateRollbackDetails(OtcPrepPutawaySlipPutAwayBO putawayParam,
                                       List<ChangeQtyLogBO> changeList,
                                       OtcPrepWorkorder workorder) {
        List<OtcPrepWorkorderDetail> rollbackDetails = putawayParam.getDetailList().stream()
                .map(OtcPrepPutawaySlipPutAwayDetailBO::getWorkorderDetail)
                .toList();

        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(rollbackDetails) == rollbackDetails.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrderDetail pickedQty"));

        // 记录操作日志
        OtcPrepWorkorderAuditLogHelper.recordLog(workorder, RollbackConstant.ROLLBACK_PICKED_QTY,
                JsonUtil.toJson(changeList), putawayParam.getNote(), BaseTypeLogEnum.OPERATION.getType());
    }

    @Override
    public List<PrepWorkorderConfirmDetailVO> rollbackList(WorkorderRollbackListQuery query) {
        Map<Long, OtcPrepWorkorder> wkMap = StreamUtils.toMap(otcPrepWorkorderService.listByIds(query.getIdList()), IdModel::getId);
        Map<Long, List<OtcPrepWorkorderDetail>> detailsMap = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(query.getIdList());

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtcPrepWorkorder workorder = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PrepWorkorderConfirmDetailVO rollback = BeanUtil.copyNew(detail, PrepWorkorderConfirmDetailVO.class);
                                PrepWorkorderConfirmVO confirm = BeanUtil.copyNew(workorder, PrepWorkorderConfirmVO.class);
                                confirm.setWorkorderStatus(workorder.getPrepWorkorderStatus());
                                rollback.setPrepWorkorder(confirm);
                                return rollback;
                            });
                })
                .toList();
    }

    @Override
    public OtcPrepWorkorder rollbackPutAwayUnits(RollbackPutawayUnitsUpdateParam param) {
        OtcPrepWorkorder prepWorkorder = otcPrepWorkorderService.getById(param.getPrepWorkorderId());

        ProcessType.checkAbnormal(prepWorkorder.getProcessType(), prepWorkorder.refNumLog(), "rollbackPutAwayUnits");

        // 工单状态校验
        OtcWorkorder workorder = otcWorkorderService.getById(prepWorkorder.getOtcWorkorderId());
        Validate.isTrue(workorder.getOtcWorkorderStatus().equals(OtcWorkorderStatusEnum.BEGIN.getStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT, workorder.refNumLog(),
                "rollbackPutAwayUnits", OtcWorkorderStatusEnum.BEGIN.getStatus(), workorder.getOtcWorkorderStatus()
        );

        // 校验
        Validate.isTrue(prepWorkorder.getPutawayQty() >= param.getRollbackQty(), String.format(ErrorMessages.INSUFFICIENT_QUANTITY,
                StringUtil.format("{} is not enough, [PutawayQty: {}] < [RollbackQty: {}]",
                        prepWorkorder.refNumLog(),
                        prepWorkorder.getPutawayQty(), param.getRollbackQty())
        ));

        // Rollback逻辑
        prepWorkorder.setPutawayQty(prepWorkorder.getPutawayQty() - param.getRollbackQty());

        List<OtcPrepWorkorderDetail> prepWorkorderDetails = otcPrepWorkorderDetailService.listByPrepWorkOrderId(prepWorkorder.getOtcWorkorderId());
        prepWorkorderDetails.forEach(obj -> {
            // Detail Put AwayQty = Detail.qty * Header.putawayQty / Header.qty
            obj.setPutawayQty(obj.getQty() * prepWorkorder.getPutawayQty() / prepWorkorder.getQty());
        });

        String oldStatus = prepWorkorder.getPrepWorkorderStatus();
        // 更新状态
        boolean isPutaway = Objects.equals(prepWorkorder.getPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.PROCESSED.getStatus());
        prepWorkorder.setPrepWorkorderStatus(isPutaway
                ? OtcPrepWorkorderStatusEnum.PICKED.getStatus()
                : oldStatus
        );

        Validate.isTrue(otcPrepWorkorderService.update(prepWorkorder) == 1, "Update Workorder PutAwayQty is fail");
        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(prepWorkorderDetails) == prepWorkorderDetails.size(),
                "Update PrepWorkorderDetail putawayQty is fail"
        );

        // 状态变更
        if (!Objects.equals(oldStatus, prepWorkorder.getPrepWorkorderStatus())) {
            OtcPrepWorkorderAuditLogHelper.recordLog(prepWorkorder, null, param.getNote());
        }

        // 记录日志
        OtcPrepWorkorderAuditLogHelper.recordLog(prepWorkorder, RollbackConstant.ROLLBACK_PUTAWAY_QTY, null,
                param.getNote(), BaseTypeLogEnum.ROLLBACK_PUTAWAY_UNITS.getType()
        );

        return prepWorkorder;

    }

    @Override
    public void cancelWithPickingSlip(List<Long> prepPickingSlipIdList) {
        // 获取拣货单下的工单
        List<OtcPrepWorkorder> workorderList = otcPrepWorkorderService.listByPrepPickingSlipIds(prepPickingSlipIdList);

        // 回滚到Begin
        workorderList.forEach(workorder -> workorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.BEGIN.getStatus()));

        // 更新工单
        Validate.isTrue(otcPrepWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update PrepWorkOrder status is fail"
        );
        OtcPrepWorkorderAuditLogHelper.recordLog(workorderList);

        // 回滚 拣货单锁 -> 工单锁
        List<Long> workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        List<OtcPrepWorkorderDetail> details = otcPrepWorkorderDetailService.listByPrepWorkOrderIds(workorderIds);
        List<Long> inventoryLockedIds = StreamUtils.distinctMap(details, OtcPrepWorkorderDetail::getInventoryLockedId);

        List<InventoryLocked> inventoryLockedList = inventoryLockedService.listByIds(inventoryLockedIds);
        inventoryLockedList.forEach(locked -> {
            locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
            locked.setFinishQty(0);
        });

        Validate.isTrue(inventoryLockedService.updateBatch(inventoryLockedList) == inventoryLockedList.size(),
                "Update InventoryLocked update fail"
        );
    }

    @Override
    public List<PrepPickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds) {
        // 处理已经创建上架单的不让他继续创建
        Map<Long, Integer> hasCreateQtyMap = otcPrepPutawaySlipDetailService.listAvailableByWorkorderIds(workorderIds)
                .stream()
                .collect(Collectors.groupingBy(OtcPrepPutawaySlipDetail::getPrepWorkorderBinLocationId, Collectors.summingInt(OtcPrepPutawaySlipDetail::getQty)));
        List<OtcPrepWorkorderBinLocation> currenWkPickInfoList = otcPrepWorkorderBinLocationService.listByPrepWorkorderIds(workorderIds);
        // 扣除上架单占用数量
        currenWkPickInfoList.forEach(obj -> obj.setQty(obj.getQty() - hasCreateQtyMap.getOrDefault(obj.getId(), 0)));

        // 赋值返回参数
        List<PrepPickingSlipUnpickDetailVO> unpickDetails = currenWkPickInfoList.stream()
                .map(obj -> {
                    PrepPickingSlipUnpickDetailVO unpick = BeanUtil.copyNew(obj, PrepPickingSlipUnpickDetailVO.class);
                    unpick.setPrepWorkorderId(obj.getOtcPrepWorkorderId());
                    unpick.setPrepWorkorderDetailId(obj.getOtcPrepWorkorderDetailId());
                    unpick.setPrepWorkorderBinLocationId(obj.getId());
                    unpick.setPrepPickingSlipId(obj.getOtcPrepPickingSlipId());
                    unpick.setPrepPickingSlipDetailId(obj.getOtcPrepPickingSlipDetailId());
                    return unpick;
                })
                .toList();

        // 拣货数量即 是Unpick时 Rollback时的 qty
        unpickDetails.stream()
                .sorted(Comparator.comparing(BasePickingSlipUnpickDetailVO::getUnpickId))
                .forEach(detail -> detail.setPickedQty(detail.getQty()));

        // 获取工单详情
        List<Long> wkDetailIds = StreamUtils.distinctMap(currenWkPickInfoList, OtcPrepWorkorderBinLocation::getOtcWorkorderDetailId);
        List<OtcPrepWorkorderDetail> wkDetails = otcPrepWorkorderDetailService.listByIds(wkDetailIds);
        Map<Long, OtcPrepWorkorderDetail> wkDetailMap = StreamUtils.toMap(wkDetails, OtcPrepWorkorderDetail::getId);

        Map<Long, Integer> putawayQtyMap = wkDetailMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, obj -> obj.getValue().getPutawayQty()));

        // 赋值putawayQty
        for (PrepPickingSlipUnpickDetailVO unpickDetail : unpickDetails) {
            Integer remaining = putawayQtyMap.getOrDefault(unpickDetail.getPrepWorkorderDetailId(), 0);
            if (remaining <= 0) {
                continue;
            }
            int putawayQty = Math.min(remaining, unpickDetail.getPickedQty());
            unpickDetail.setPutawayQty(putawayQty);
            putawayQtyMap.put(unpickDetail.getPrepWorkorderDetailId(), remaining - putawayQty);
        }

        return unpickDetails;
    }

    @Override
    public PrepWorkorderFinishConfirmVO finishRollbackConfirm(WorkorderRollbackListQuery query) {
        List<PrepWorkorderConfirmDetailVO> prepWorkorderList = this.confirmDetailList(query.getIdList());

        List<Long> prepWorkorderIds = prepWorkorderList.stream()
                .map(PrepWorkorderConfirmDetailVO::getPrepWorkorder)
                .map(WorkorderConfirmVO::getId)
                .distinct().toList();

        // Prep上架单完成确认
        List<PrepPutawaySlipConfirmDetailVO> prepPutawaySlipList = otcPrepPutawaySlipService.confirmDetailList(prepWorkorderIds);

        return new PrepWorkorderFinishConfirmVO(prepWorkorderList, prepPutawaySlipList);
    }

    @Override
    public void split(List<OtcWorkorderSplitBO> splitHolders) {
        // Prep工单：拆单
        var workorderDetailIds = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getDetailHolders)
                .flatMap(Collection::stream)
                .map(OtcWorkorderSplitDetailBO::getDetail)
                .map(IdModel::getId)
                .toList();

        var prepWorkorderList = otcPrepWorkorderService.listByWorkorderDetailIds(workorderDetailIds);
        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return;
        }

        //todo: 这个是临时限制，后面再慢慢支持
        for (var prepWorkorder : prepWorkorderList) {
            if (!OtcPrepWorkorderStatusEnum.canCancelStatuses().contains(prepWorkorder.getPrepWorkorderStatus())) {
                throw new BusinessException(StringUtil.format("{} current status is {} , can not support cancel", prepWorkorder.refNumLog(), prepWorkorder.getPrepWorkorderStatus()));
            }
        }

        var prepWkDetails = otcPrepWorkorderDetailService.listByPrepWorkOrderIds(StreamUtils.distinctMap(prepWorkorderList, IdModel::getId));
        var prepWkDetailsGroupMap = StreamUtils.groupBy(prepWkDetails, OtcPrepWorkorderDetail::getOtcPrepWorkorderId);

        var splitDetailHolders = splitHolders.stream().map(OtcWorkorderSplitBO::getDetailHolders).flatMap(Collection::stream).toList();
        // 拆单，Prep工单，数量、上架数量拆分
        for (var splitHolder : splitDetailHolders) {
            // 设置Prep工单拆单信息
            splitHolder.setPrepWorkorderHolders(prepWorkorderList.stream()
                    .filter(obj -> Objects.equals(obj.getOtcWorkorderDetailId(), splitHolder.getDetail().getId()))
                    .map(prep -> splitSingle(splitHolder, prep, prepWkDetailsGroupMap))
                    .toList()
            );
        }

        // PrepWorkorder & Split PrepWorkorder:  Update 状态
        OtcWorkorderHelper.refreshPrepStatus(prepWorkorderList, prepWkDetails);
        OtcWorkorderHelper.refreshPrepStatus(splitDetailHolders.stream()
                        .flatMap(obj -> obj.getPrepWorkorderHolders().stream().map(OtcPrepWorkorderSplitBO::getSplitPrepWorkorder))
                        .toList(),
                splitDetailHolders.stream()
                        .flatMap(obj -> obj.getPrepWorkorderHolders().stream().map(OtcPrepWorkorderSplitBO::getPrepDetailHolders))
                        .flatMap(Collection::stream)
                        .map(OtcPrepWorkorderSplitDetailBO::getSplitPrepDetail)
                        .toList()
        );

        // Step: Prep拣货单 拆单
        otcPrepPickingSlipSpecialService.split(splitHolders);

        var splitPrepHolders = splitDetailHolders.stream()
                .map(OtcWorkorderSplitDetailBO::getPrepWorkorderHolders)
                .flatMap(Collection::stream)
                .toList();

        // Step: 锁拆单
        this.lockSplit(splitPrepHolders);

        // 更新工单状态
        Validate.isTrue(otcPrepWorkorderService.updateBatch(prepWorkorderList) == prepWorkorderList.size(),
                "Update PrepWorkOrder status failed"
        );

        var details = splitDetailHolders.stream()
                .flatMap(obj -> obj.getPrepWorkorderHolders().stream()
                        .flatMap(o -> o.getPrepDetailHolders().stream().map(OtcPrepWorkorderSplitDetailBO::getPrepDetail)))
                .toList();

        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(details) == details.size(),
                "Update PrepWorkOrderDetail status failed"
        );

        // 拆单入库
        var splitPrepWorkorderList = splitPrepHolders.stream()
                .map(OtcPrepWorkorderSplitBO::getSplitPrepWorkorder)
                .toList();
        otcPrepWorkorderService.insertBatch(splitPrepWorkorderList);

        // 拆单详情
        var splitDetails = splitPrepHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream().map(OtcPrepWorkorderSplitDetailBO::getSplitPrepDetail))
                .toList();

        otcPrepWorkorderDetailService.insertBatch(splitDetails);

    }

    // ////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * @param context
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startCancel(OtcRequestCancelContext context) {
        if (ObjectUtil.isEmpty(context.getWorkorderList())) {
            return;
        }

        // 获取所有预提工单
        List<OtcPrepWorkorder> prepWorkorderList = otcPrepWorkorderService.listByWorkOrderIdList(context.getWorkorderIdListByWorkorderList());

        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return;
        }

        context.setPrepWorkorderList(prepWorkorderList);

        // 获取所有预提工单ID
        List<Long> prepWorkorderIds = StreamUtils.distinctMap(prepWorkorderList, IdModel::getId);

        // 查询预提工单明细
        Map<Long, List<OtcPrepWorkorderDetail>> detailsMap = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(prepWorkorderIds);
        context.setPrepWorkorderDetailMap(detailsMap);

        // 预加载 PrepWorkorderBinLocation，放入 context
        if (!ObjectUtil.isEmpty(prepWorkorderIds)) {
            List<OtcPrepWorkorderBinLocation> prepBinLocations = otcPrepWorkorderBinLocationService.listByPrepWorkorderIds(prepWorkorderIds);
            if (!ObjectUtil.isEmpty(prepBinLocations)) {
                context.setPrepWorkorderBinLocationList(prepBinLocations);
            }
        }

        otcPrepPickingSlipSpecialService.startCancel(context);

        //更新工单状态
        Validate.isTrue(otcPrepWorkorderService.updateBatch(prepWorkorderList) == prepWorkorderList.size(), "Update prepWorkorderList cancel is fail");

    }

    /**
     * 获取工单对应的Prep拣货单列表
     *
     * @param workorderId 工单ID
     * @return Prep拣货单列表
     */
    @Override
    public List<OtcPrepPickingSlip> getPickingSlipsByWorkorderId(Long workorderId) {
        if (workorderId == null) {
            return Collections.emptyList();
        }

        // 查询该工单的prep工单
        List<OtcPrepWorkorder> prepWorkorders = otcPrepWorkorderService.listByWorkOrderIdList(Collections.singletonList(workorderId));
        if (ObjectUtil.isEmpty(prepWorkorders)) {
            return Collections.emptyList();
        }

        // 获取prep工单ID列表
        List<Long> prepWorkorderIds = StreamUtils.distinctMap(prepWorkorders, IdModel::getId);

        // 使用prep工单的拣货单ID字段查询
        List<Long> prepPickingSlipIds = prepWorkorders.stream()
                .map(OtcPrepWorkorder::getOtcPrepPickingSlipId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (ObjectUtil.isEmpty(prepPickingSlipIds)) {
            return Collections.emptyList();
        }

        // 查询prep工单对应的prep拣货单
        return otcPrepPickingSlipService.listByIds(prepPickingSlipIds);
    }

    /**
     * 添加释放库存预留的参数
     *
     * @param workorderId           工单ID
     * @param releaseReservedParams 需要释放的库存预留参数列表
     */
    private void addReleaseReservedParams(Long workorderId, List<InventoryReleaseReservedParam> releaseReservedParams) {
        if (workorderId == null) {
            return;
        }

        // 查询该工单的prep工单
        List<OtcPrepWorkorder> prepWorkorders = otcPrepWorkorderService.listByWorkOrderIdList(Collections.singletonList(workorderId));
        if (ObjectUtil.isEmpty(prepWorkorders)) {
            return;
        }

        // 收集所有预留ID和数量
        for (OtcPrepWorkorder prepWorkorder : prepWorkorders) {
            Long inventoryReserveId = prepWorkorder.getInventoryReserveId();
            if (inventoryReserveId != null) {
                InventoryReleaseReservedParam param = new InventoryReleaseReservedParam();
                param.setId(inventoryReserveId);
                param.setQty(prepWorkorder.getQty()); // 使用工单数量作为释放数量
                releaseReservedParams.add(param);
            }
        }
    }

    /**
     * 释放工单的库存预留
     *
     * @param workorderId 工单ID
     */
    @Override
    public void releaseInventoryReserved(Long workorderId) {
        if (workorderId == null) {
            return;
        }

        // 收集库存预留参数
        List<InventoryReleaseReservedParam> releaseParams = new ArrayList<>();
        addReleaseReservedParams(workorderId, releaseParams);

        // 执行批量释放
        if (!releaseParams.isEmpty()) {
            releaseInventoryReserved(releaseParams);
        }
    }

    /**
     * 批量释放库存预留
     *
     * @param releaseReservedParams 需要释放的库存预留参数列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releaseInventoryReserved(List<InventoryReleaseReservedParam> releaseReservedParams) {
        if (ObjectUtil.isEmpty(releaseReservedParams)) {
            return;
        }

        // 将InventoryReleaseReservedParam转换为InventoryReleaseLockedParam
        List<InventoryReleaseLockedParam> lockedParams = new ArrayList<>();
        for (InventoryReleaseReservedParam param : releaseReservedParams) {
            InventoryReleaseLockedParam lockedParam = new InventoryReleaseLockedParam();
            lockedParam.setId(param.getId());
            lockedParam.setQty(param.getQty());
            lockedParams.add(lockedParam);
        }

        // 调用库存预留服务释放预留库存
        inventoryReserveService.cancelReserveInventory(lockedParams);
    }

    /**
     * @param context
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doCancel(OtcRequestCancelContext context) {
        // 检查是否有预提工单
        if (ObjectUtil.isEmpty(context.getPrepWorkorderList())) {
            return;
        }

        // 需要释放库存预留的参数列表
        List<InventoryReleaseReservedParam> releaseReservedParams = new ArrayList<>();

        // 需要释放库存锁定的参数列表
        List<InventoryReleaseLockedParam> releaseLockedParams = new ArrayList<>();

        // 需要更新的工单列表
        List<OtcPrepWorkorder> updatePrepWorkorders = new ArrayList<>();

        // // 处理拣货单的场景分类
        // processPrepPickingSlipScenarios(context);

        // 处理每个预提工单
        for (OtcPrepWorkorder prepWorkorder : context.getPrepWorkorderList()) {

            //判断 PrepWorkorder 状态，如果已经Processed,不需要处理了
            if (OtcPrepWorkorderStatusEnum.finishStatuses().contains(prepWorkorder.getPrepWorkorderStatus())) {
                prepWorkorder.setProcessType(ProcessType.NORMAL.getType());
                updatePrepWorkorders.add(prepWorkorder);
                continue;
            }

            // 获取工单明细
            List<OtcPrepWorkorderDetail> currentDetails = context.getPrepWorkorderDetailMap().get(prepWorkorder.getId());

            // 是否有拣货单
            boolean hasPickingSlip = prepWorkorder.getOtcPrepPickingSlipId() != null;

            if (!hasPickingSlip) {
                // 场景1: 没生成拣货单，直接取消 & 释放库存预留
                processCancelWithoutPickingSlip(prepWorkorder, currentDetails, releaseReservedParams, releaseLockedParams, updatePrepWorkorders);
            } else {
                // 处理有拣货单的情况
                processCancelWithPickingSlip(prepWorkorder, currentDetails, context, releaseReservedParams, releaseLockedParams, updatePrepWorkorders);
            }
        }

        // 批量释放库存预留
        if (!releaseReservedParams.isEmpty()) {
            releaseInventoryReserved(releaseReservedParams);
        }

        // 批量释放库存锁定
        if (!releaseLockedParams.isEmpty()) {
            inventoryLockedService.releaseLockedInventory(releaseLockedParams);
        }

        // 拣货单
        otcPrepPickingSlipSpecialService.doCancel(context);

        // 批量更新工单状态
        if (!updatePrepWorkorders.isEmpty()) {
            Validate.isTrue(otcPrepWorkorderService.updateBatch(updatePrepWorkorders) == updatePrepWorkorders.size(),
                    String.format(ErrorMessages.OPERATION_FAILED, "update PrepWorkOrder status"));
            //todo: 这里不准确，PrepWorkorder 1. 还没生成拣货单 2. 拣货单还没Begin
            // 记录日志
            OtcPrepWorkorderAuditLogHelper.recordLog(updatePrepWorkorders, "Cancel", "Not Pick, System Cancelled", context.getNote());
        }
    }

    /**
     * 处理PrepPickingSlip的不同场景分类
     *
     * @param context 取消上下文
     */
    private void processPrepPickingSlipScenarios(OtcRequestCancelContext context) {
        if (ObjectUtil.isEmpty(context.getPrepPickingSlipList())) {
            return;
        }

        // 获取所有相关的PrepPickingSlip详情
        List<Long> prepPickingSlipIds = StreamUtils.distinctMap(context.getPrepPickingSlipList(), IdModel::getId);
        List<OtcPrepPickingSlipDetail> allPrepPickingSlipDetails = otcPrepPickingSlipDetailService.listByOtcPrepPickingSlipIds(prepPickingSlipIds);
        context.addPrepPickingSlipDetails(allPrepPickingSlipDetails);

        // 分类场景2和场景3
        for (OtcPrepPickingSlip pickingSlip : context.getPrepPickingSlipList()) {
            // 从工单列表中找到与当前拣货单关联的工单
            List<OtcPrepWorkorder> relatedWorkorders = context.getPrepWorkorderList().stream()
                    .filter(workorder -> Objects.equals(workorder.getOtcPrepPickingSlipId(), pickingSlip.getId()))
                    .toList();

            if (relatedWorkorders.isEmpty()) {
                continue;
            }

            OtcPrepWorkorder workorder = relatedWorkorders.get(0);

            // 判断拣货单状态
            boolean isPickingSlipBegin = !OtcPrepPickingSlipStatusEnum.canPickProcessStatus(pickingSlip.getPrepPickingSlipStatus());

            if (!isPickingSlipBegin) {
                // 场景2: 拣货单未开始(NEW状态)，可以直接扣减数量
                List<OtcPrepWorkorderDetail> workorderDetails = context.getPrepWorkorderDetailMap().get(workorder.getId());
                OtcRequestCancelContext.PrepDirectCancelItem directCancelItem =
                        new OtcRequestCancelContext.PrepDirectCancelItem(pickingSlip, workorder, workorderDetails);
                context.addPrepDirectCancelItem(pickingSlip, workorder, workorderDetails);
            } else {
                // 场景3: 拣货单已开始，需要生成上架单
                List<OtcPrepPickingSlipDetail> pickingSlipDetails = context.getPrepPickingSlipDetailsByPrepPickingSlipId(pickingSlip.getId());
                List<OtcPrepWorkorderDetail> workorderDetails = context.getPrepWorkorderDetailMap().get(workorder.getId());

                if (!ObjectUtil.isEmpty(pickingSlipDetails) && pickingSlipDetails.stream().anyMatch(detail -> detail.getPickedQty() > 0)) {
                    OtcRequestCancelContext.PrepUnpickItem unpickItem =
                            new OtcRequestCancelContext.PrepUnpickItem(pickingSlip, workorder, pickingSlipDetails, workorderDetails);
                    context.addPrepUnpickItem(pickingSlip, workorder, pickingSlipDetails, workorderDetails);
                }
            }
        }
    }

    /**
     * 处理没有拣货单的工单取消
     *
     * @param prepWorkorder         预提工单
     * @param details               预提工单明细
     * @param releaseReservedParams 释放预留参数列表
     * @param releaseLockedParams   释放锁定参数列表
     * @param updatePrepWorkorders  需要更新的预提工单列表
     */
    private void processCancelWithoutPickingSlip(
            OtcPrepWorkorder prepWorkorder,
            List<OtcPrepWorkorderDetail> details,
            List<InventoryReleaseReservedParam> releaseReservedParams,
            List<InventoryReleaseLockedParam> releaseLockedParams,
            List<OtcPrepWorkorder> updatePrepWorkorders) {
        // 直接取消 & 释放库存预留
        prepWorkorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.CANCELLED.getStatus());
        prepWorkorder.setProcessType(ProcessType.NORMAL.getType());

        // 收集释放预留库存的参数
        Long inventoryReserveId = prepWorkorder.getInventoryReserveId();
        if (inventoryReserveId != null && prepWorkorder.getQty() > 0) {
            // 使用工单数量作为释放数量
            InventoryReleaseReservedParam param = new InventoryReleaseReservedParam();
            param.setId(inventoryReserveId);
            param.setQty(prepWorkorder.getQty());
            releaseReservedParams.add(param);
        }

        // 收集释放库存锁定的参数
        if (!ObjectUtil.isEmpty(details)) {
            addReleaseLockedParams(details, releaseLockedParams);
        }

        updatePrepWorkorders.add(prepWorkorder);
    }

    /**
     * 处理有拣货单的工单取消
     *
     * @param prepWorkorder         预提工单
     * @param details               预提工单明细
     * @param context               请求上下文
     * @param releaseReservedParams 释放预留参数列表
     * @param releaseLockedParams   释放锁定参数列表
     * @param updatePrepWorkorders  需要更新的预提工单列表
     */
    private void processCancelWithPickingSlip(
            OtcPrepWorkorder prepWorkorder,
            List<OtcPrepWorkorderDetail> details,
            OtcRequestCancelContext context,
            List<InventoryReleaseReservedParam> releaseReservedParams,
            List<InventoryReleaseLockedParam> releaseLockedParams,
            List<OtcPrepWorkorder> updatePrepWorkorders) {
        // 拣货单ID
        Long prepPickingSlipId = prepWorkorder.getOtcPrepPickingSlipId();

        if (prepPickingSlipId == null) {
            return;
        }

        // 查询拣货单
        OtcPrepPickingSlip pickingSlip = context.getPrepPickingSlipById(prepPickingSlipId);

        if (pickingSlip == null) {
            return;
        }

        // 判断拣货单状态
        boolean isPickingSlipBegin = Objects.equals(pickingSlip.getPrintStatus(), PrintStatusEnum.SUCCESS.getStatus()) ||
                !Objects.equals(pickingSlip.getPrepPickingSlipStatus(), OtcPrepPickingSlipStatusEnum.NEW.getStatus());

        if (!isPickingSlipBegin) {

            var prepPickingSlips = context.getPrepPickingSlipListByWorkorderId(prepWorkorder.getOtcWorkorderId());

            // 检查prep拣货单是否打印
            boolean isPrepPickingSlipPrinted = prepPickingSlips.stream()
                    .anyMatch(slip -> Objects.equals(slip.getPrintStatus(), PrintStatusEnum.SUCCESS.getStatus()));

            boolean allPrepPickingSlipNotBegin = !isPrepPickingSlipPrinted && prepPickingSlips.stream()
                    .allMatch(slip -> Objects.equals(slip.getPrepPickingSlipStatus(), OtcPrepPickingSlipStatusEnum.NEW.getStatus()));

            //说明其他的Prep 拣货单已开始，那么就需要一致做下去,在 workorder 生成拣货单那边处理
            //这种情况比较少
            if (!allPrepPickingSlipNotBegin) {
                return;
            }

            // 场景2: 拣货单未开始，可以直接取消
            prepWorkorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.CANCELLED.getStatus());
            prepWorkorder.setProcessType(ProcessType.NORMAL.getType());

            // 收集释放预留库存的参数
            Long inventoryReserveId = prepWorkorder.getInventoryReserveId();
            if (inventoryReserveId != null && prepWorkorder.getQty() > 0) {
                InventoryReleaseReservedParam param = new InventoryReleaseReservedParam();
                param.setId(inventoryReserveId);
                param.setQty(prepWorkorder.getQty());
                releaseReservedParams.add(param);
            }

            // 收集释放库存锁定的参数
            if (!ObjectUtil.isEmpty(details)) {
                addReleaseLockedParams(details, releaseLockedParams);
            }

            updatePrepWorkorders.add(prepWorkorder);

            // 记录到context中，便于pickingSlip处理时只扣减Qty而不生成上架单
            context.addPrepDirectCancelItem(pickingSlip, prepWorkorder, details);
        }
        // 场景3和4: 拣货单已开始或已完成，暂时不用处理--> 这种就需要正常走下去
    }

    /**
     * 添加释放库存锁定的参数
     *
     * @param details       预提工单明细
     * @param releaseParams 释放参数列表
     */
    private void addReleaseLockedParams(List<OtcPrepWorkorderDetail> details, List<InventoryReleaseLockedParam> releaseParams) {
        if (ObjectUtil.isEmpty(details)) {
            return;
        }

        Map<Long, Integer> lockedQtyMap = details.stream()
                .filter(detail -> detail.getInventoryLockedId() != null && detail.getQty() != null && detail.getQty() > 0)
                .collect(Collectors.groupingBy(
                        OtcPrepWorkorderDetail::getInventoryLockedId,
                        Collectors.summingInt(OtcPrepWorkorderDetail::getQty)
                ));

        lockedQtyMap.forEach((lockedId, qty) -> {
            InventoryReleaseLockedParam param = new InventoryReleaseLockedParam();
            param.setId(lockedId);
            param.setQty(qty);
            releaseParams.add(param);
        });
    }

    /**
     * 锁拆单
     *
     * @param splitPrepHolders prepHolder
     */
    private void lockSplit(List<OtcPrepWorkorderSplitBO> splitPrepHolders) {
        // 原单 释放锁
        var lockedIds = splitPrepHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream())
                .map(obj -> obj.getSplitPrepDetail().getInventoryLockedId())
                .toList();
        var lockedList = inventoryLockedService.listByIds(lockedIds);
        var lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // InventoryLocked 拆单
        var splitLockedList = splitPrepHolders.stream()
                .flatMap(holder -> holder.getPrepDetailHolders().stream()
                        .map(detailHolder -> {
                            var currentLocked = lockedMap.get(detailHolder.getPrepDetail().getInventoryLockedId());
                            currentLocked.setQty(currentLocked.getQty() - detailHolder.getSplitQty());
                            // FinishQty
                            var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                                    ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                            currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                            LockedHelper.statusRefresh(currentLocked);

                            // InventoryLocked 拆单
                            var splitLocked = BeanUtil.copyNew(currentLocked, InventoryLocked.class);
                            splitLocked.setId(IdWorker.getId());

                            // 设置数量更新状态
                            splitLocked.setQty(detailHolder.getSplitQty());
                            splitLocked.setFinishQty(splitFinishQty);
                            LockedHelper.statusRefresh(splitLocked);

                            splitLocked.setRefTableShowRefNum(holder.getSplitPrepWorkorder().getRefNum());
                            var splitDetail = detailHolder.getSplitPrepDetail();
                            splitLocked.setRefTableName(String.valueOf(splitDetail.getLineNum()));
                            splitLocked.setRefTableId(splitDetail.getId());
                            return splitLocked;
                        }))
                .toList();
        inventoryLockedService.insertBatch(splitLockedList);

        Validate.isTrue(inventoryLockedService.updateBatch(lockedList) == lockedList.size(),
                "Update InventoryLocked qty fail"
        );
    }

    /**
     * 确认列表
     *
     * @param workorderIds 工单id
     * @return /
     */
    public List<PrepWorkorderConfirmDetailVO> confirmDetailList(Collection<Long> workorderIds) {
        List<OtcPrepWorkorder> prepWorkorderList = otcPrepWorkorderService.listByIds(workorderIds);
        Map<Long, OtcPrepWorkorder> wkMap = StreamUtils.toMap(prepWorkorderList, IdModel::getId);
        List<Long> prepWorkorderIds = StreamUtils.distinctMap(prepWorkorderList, IdModel::getId);
        Map<Long, List<OtcPrepWorkorderDetail>> detailsMap = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(prepWorkorderIds);

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtcPrepWorkorder workorder = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PrepWorkorderConfirmDetailVO rollback = BeanUtil.copyNew(detail, PrepWorkorderConfirmDetailVO.class);
                                PrepWorkorderConfirmVO confirm = BeanUtil.copyNew(workorder, PrepWorkorderConfirmVO.class);
                                confirm.setWorkorderStatus(workorder.getPrepWorkorderStatus());
                                rollback.setPrepWorkorder(confirm);
                                return rollback;
                            });
                })
                .toList();
    }

    /**
     * 流程触发
     *
     * @param process       流程参数
     * @param workorderList 工单
     */
    private void processTriggering(WorkorderProcessBO process, List<OtcPrepWorkorder> workorderList) {
        String type = process.getProcessType().getType();
        workorderList.forEach(obj -> obj.setProcessType(type));

        Validate.isTrue(otcPrepWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status [{}] failed", type
        );

        // 记录日志
        OtcPrepWorkorderAuditLogHelper.recordLog(workorderList, type, null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());

        // 拣货单 Rollback
        process.setPrepPickingSlipIds(StreamUtils.distinctMap(workorderList, OtcPrepWorkorder::getOtcPrepPickingSlipId));

        // Prep拣货单: 触发流程
        otcPrepPickingSlipSpecialService.processTriggering(process);
    }

    /**
     * 获取并校验start工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtcPrepWorkorder> getAndCheckStartWorkorder(WorkorderProcessBO process) {
        // 工单
        List<OtcPrepWorkorder> workorderList = otcPrepWorkorderService.listByWorkOrderIdList(process.getHasPrepWorkorderIds());

        Validate.notEmpty(workorderList, "idList: {} WorkOrder is empty", process.getHasPrepWorkorderIds());

        String type = process.getProcessType().getType();
        workorderList.forEach(obj -> Validate.isTrue(ProcessType.NORMAL.getType().equals(obj.getProcessType()),
                ErrorConstant.STATUS_ERROR_FORMAT,
                obj.refNumLog(), "start" + type, ProcessType.NORMAL.getType(), obj.getProcessType()
        ));

        return workorderList;
    }

    private List<OtcPrepWorkorder> getAndCheckFinishWorkorder(WorkorderProcessBO process) {
        List<OtcPrepWorkorder> workorderList = otcPrepWorkorderService.listByWorkOrderIdList(process.getHasPrepWorkorderIds());

        Validate.notEmpty(workorderList, "idList: {} WorkOrder is empty", process.getHasPrepWorkorderIds());

        // 校验上架是否全部完成
        otcPrepPutawaySlipService.finishRollback(workorderList);

        workorderList.forEach(obj -> {
            ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "finish");

            obj.setProcessType(ProcessType.NORMAL.getType());
        });
        Validate.isTrue(otcPrepWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status [{}] failed", ProcessType.NORMAL.getType()
        );
        return workorderList;
    }

}
