/**
 * <p>
 * 基础枚举常量包
 * </p>
 * 
 * <p>
 * 该包包含了系统中通用的基础枚举类型定义，这些枚举被多个业务模块共享使用。
 * 基础枚举主要定义了系统中各种状态、类型和标志等常量值，确保系统中使用的常量值保持一致。
 * </p>
 * 
 * <p>
 * 主要包含的枚举类型：
 * 1. 请求状态枚举（RequestStatusEnum）
 * 2. 处理类型枚举（ProcessType）
 * 3. 预拣货单类型枚举（PrepWorkOrderTypeEnum）
 * 4. 日志类型枚举（BaseTypeLogEnum）
 * 5. 打印状态枚举（PrintStatusEnum）
 * 6. 其他基础状态和类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
package cn.need.cloud.biz.client.constant.enums.base;
