package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Otb出库工单状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtbWorkorderEnum {

    /**
     * 新建
     */
    NEW("New"),
    /**
     * 开始
     */
    BEGIN("Begin"),

    /**
     * 完成构建拣货单
     */
    IN_PICKING("InPicking"),

    /**
     * 完成拣货
     */
    PICKED("Picked"),

    /**
     * 完成打包状态
     */
    PACKED("Packed"),

    WAIT_CHANNEL_CONFIRM("WaitChannelConfirm"),


    CHANNEL_CONFIRM("ChannelConfirmed"),

    SHIPED("Shipped"),

    CANCELLED("Cancelled"),

    PART_CANCELLED("PartCancelled"),

    ON_HOLD("OnHold"),

    LOCKED("Locked"),

    ;

    @EnumValue
    @JsonValue
    private final String status;

    public static List<String> canStartStatuses() {
        return List.of(
                NEW.getStatus(),
                BEGIN.getStatus(),
                IN_PICKING.getStatus(),
                PICKED.getStatus()
        );
    }
}
