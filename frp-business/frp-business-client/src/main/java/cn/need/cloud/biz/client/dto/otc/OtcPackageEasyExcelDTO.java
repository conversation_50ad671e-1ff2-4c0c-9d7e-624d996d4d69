package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.core.date.pattern.DatePattern;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(20)
@ColumnWidth(16)
public class OtcPackageEasyExcelDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty("WarehouseCode")
    private String warehouseCode;

    @ExcelIgnore
    private Long warehouseId;

    @ExcelProperty("RefNum")
    private String refNum;

    @ExcelProperty("TrackingNum")
    private String trackingNum;

    @ExcelProperty("Request_RefNum")
    private String requestRefNum;

    @ExcelProperty("Request_RequestRefNum")
    private String requestRequestRefNum;

    @ExcelProperty("Request_Channel")
    private String requestChannel;

    @ExcelProperty("PackageStatus")
    private String packageStatus;

    @ExcelProperty("ShipMethod")
    private String shipMethod;

    @ExcelProperty("ShipCarrier")
    private String shipCarrier;

    @ExcelProperty("IsShipExpress")
    private Boolean shipExpressFlag;

    @ExcelProperty("Note")
    private String note;

    @ExcelProperty("ReadyToShipTime")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    // 防止时间字段宽度不够导致显示【######】
    @ColumnWidth(value = 22)
    private LocalDateTime readyToShipTime;

    @ExcelProperty("ShippedTime")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    // 防止时间字段宽度不够导致显示【######】
    @ColumnWidth(value = 22)
    private LocalDateTime shippedTime;
}
