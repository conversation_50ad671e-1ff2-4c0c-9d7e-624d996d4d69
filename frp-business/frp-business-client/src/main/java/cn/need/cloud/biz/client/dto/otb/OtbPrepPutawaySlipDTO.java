package cn.need.cloud.biz.client.dto.otb;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC上架单 dto对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtbPrepPutawaySlipDTO extends SuperDTO {


    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 备注
     */
    private String note;

    /**
     * 拣货单id
     */
    private Long pickingSlipId;

    /**
     * 状态
     */
    private String putawaySlipStatus;

    /**
     * 编码
     */
    private String refNum;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 工单id
     */
    private Long workorderId;

}