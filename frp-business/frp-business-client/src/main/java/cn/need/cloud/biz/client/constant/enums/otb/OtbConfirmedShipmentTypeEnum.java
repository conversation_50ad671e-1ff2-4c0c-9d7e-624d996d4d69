package cn.need.cloud.biz.client.constant.enums.otb;

import cn.need.framework.common.core.exception.unchecked.BusinessException;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * Otc预工单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtbConfirmedShipmentTypeEnum {

    /**
     * 不变
     */
    NO_CHANGE("NoChange"),

    /**
     * 大件拆小
     */
    SPLIT_PALLET("SplitPallet"),

    /**
     * 小件打托变大
     */
    CREATE_PALLET("CreatePallet");

    @EnumValue
    @JsonValue
    private final String type;

    public static OtbConfirmedShipmentTypeEnum getByType(String type) {
        for (OtbConfirmedShipmentTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new BusinessException("type is not exist");
    }

}

