package cn.need.cloud.biz.client.dto.req.product;

import cn.need.cloud.biz.client.dto.req.base.BaseLogisticPartnerAndWarehouseReqDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Set;


/**
 * 产品 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品尺寸变更query对象")
public class ProductDimensionChangeQueryReqDTO extends BaseLogisticPartnerAndWarehouseReqDTO {
    @Serial
    private static final long serialVersionUID = -2089507885277899981L;


    /**
     * 起始修改日期
     */
    @Schema(description = "起始修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "lastShipDate cannot be empty")
    private LocalDateTime startLastModificationTime;


    /**
     * 最后修改日期
     */
    @Schema(description = "最后修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "lastShipDate cannot be empty")
    private LocalDateTime endLastModificationTime;



}