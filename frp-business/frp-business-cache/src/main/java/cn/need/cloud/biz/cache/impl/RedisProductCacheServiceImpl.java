package cn.need.cloud.biz.cache.impl;


import cn.need.cloud.biz.cache.ProductCacheService;
import cn.need.cloud.biz.cache.ProductRedisTemplate;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.framework.common.core.convert.Convert;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Collection;
import java.util.List;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisProductCacheServiceImpl implements ProductCacheService {

    /**
     * redisTemplate对象，需要子类注入
     */
    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * 租户Template对象
     */
    private final ProductRedisTemplate template;


    public RedisProductCacheServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        Validate.notNull(redisTemplate, "redisTemplate can't be null!");
        this.redisTemplate = redisTemplate;
        this.template = new ProductRedisTemplate(this.redisTemplate);
    }

    @Override
    public ProductCache getById(Long productId) {
        return this.template.hash().get(StringUtil.toString(productId));
    }

    @Override
    public List<ProductCache> listByIds(Collection<Long> productIds) {
        return this.template.hash().multiGet(Convert.toList(String.class, productIds));
    }

}
