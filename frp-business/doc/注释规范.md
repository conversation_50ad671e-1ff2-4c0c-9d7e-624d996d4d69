# Java代码注释规范

## 1. 类注释规范

每个类都应该有Javadoc注释，包含以下内容：

```java
/**
 * 类的详细描述，说明类的主要功能和用途。
 * 如果类实现了特定接口或继承自特定类，应当说明实现或继承的目的。
 * 多行描述时，每行应有合理的长度，避免过长。
 *
 * <AUTHOR>
 * @since 创建日期（格式：yyyy-MM-dd）
 * @version 版本号
 */
```

## 2. 接口注释规范

接口注释类似于类注释，但应强调接口的目的和约定：

```java
/**
 * 接口的详细描述，说明接口定义的契约和用途。
 * 应包含实现该接口需要遵循的规则和约定。
 *
 * <AUTHOR>
 * @since 创建日期（格式：yyyy-MM-dd）
 */
```

## 3. 方法注释规范

所有公共方法都应该有Javadoc注释：

```java
/**
 * 方法的详细描述，说明方法的功能和作用。
 * 如果方法实现了接口方法或重写了父类方法，应说明实现的特殊逻辑。
 *
 * @param 参数名 参数的描述和用途
 * @return 返回值的描述（非void方法）
 * @throws 异常类名 抛出异常的条件描述
 */
```

## 4. 字段注释规范

类的字段应该有注释说明其用途：

```java
/**
 * 字段的描述，说明其用途和可能的取值范围
 */
private String fieldName;
```

## 5. 枚举注释规范

枚举类及其常量应有清晰的注释：

```java
/**
 * 枚举类的详细描述
 *
 * <AUTHOR>
 * @since 创建日期（格式：yyyy-MM-dd）
 */
public enum ExampleEnum {
    
    /**
     * 枚举常量的详细描述
     */
    CONSTANT_ONE,
    
    /**
     * 枚举常量的详细描述
     */
    CONSTANT_TWO
}
```

## 6. 常量注释规范

常量应该有注释说明其用途和值的含义：

```java
/**
 * 常量的详细描述，说明其用途和值的含义
 */
public static final String CONSTANT_NAME = "value";
```

## 7. 代码内行注释

对于复杂的代码逻辑，应使用行注释进行说明：

```java
// 这里是对复杂逻辑的解释说明
// 多行注释时应保持缩进一致
```

## 8. 注释风格统一

- 所有注释应使用中文，保持语言一致性
- 注释应简洁明了，避免不必要的冗长描述
- 注释应与代码保持同步，当代码变更时及时更新注释
- 避免使用无意义的注释（如仅重复方法名或参数名）
- 对于关键的业务逻辑，应提供详细的注释说明

## 9. 包注释规范

每个包应该有package-info.java文件，描述包的整体用途：

```java
/**
 * 包的整体描述，说明该包的职责和包含的主要组件。
 * 可以包含该包与系统其他部分的交互关系。
 *
 * <AUTHOR>
 * @since 创建日期（格式：yyyy-MM-dd）
 */
package cn.need.cloud.biz.example;
```

## 10. 注释示例

### 类注释示例

```java
/**
 * 订单服务实现类，负责处理订单的创建、查询、修改和删除等操作。
 * 实现了OrderService接口，提供订单全生命周期的管理功能。
 *
 * <AUTHOR>
 * @since 2023-10-15
 * @version 1.0.0
 */
@Service
public class OrderServiceImpl implements OrderService {
    // 类内容
}
```

### 方法注释示例

```java
/**
 * 根据订单号查询订单详情信息。
 * 该方法会检查订单的有效性，并加载关联的商品和支付信息。
 *
 * @param orderNo 订单编号，不能为空
 * @return 订单详情对象，如果订单不存在则返回null
 * @throws IllegalArgumentException 当orderNo为空时抛出
 * @throws BusinessException 当查询过程中出现业务异常时抛出
 */
public OrderDetail getOrderByNo(String orderNo) {
    // 方法实现
}
```

### 字段注释示例

```java
/**
 * 用户ID，系统内唯一标识符
 */
private Long userId;

/**
 * 订单状态，表示订单当前所处的处理阶段
 * @see OrderStatusEnum
 */
private String orderStatus;
``` 