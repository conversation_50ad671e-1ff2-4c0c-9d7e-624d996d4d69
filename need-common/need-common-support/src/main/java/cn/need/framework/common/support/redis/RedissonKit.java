package cn.need.framework.common.support.redis;

import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.ExceptionUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.config.*;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * 单例方法，redis锁的工具类.
 *
 * <AUTHOR>
 */
@Slf4j
public class RedissonKit {

    /**
     * redis锁key值的前缀
     */
    public final static String LOCK_PREFIX = "LOCK:";

    /**
     * redis锁客户端
     */
    private final RedissonClient client;

    /**
     * 私有化当前示例
     */
    private static volatile RedissonKit instance;




    /**
     * 懒汉构造方式，从spring容器中获取Redisson客户端
     */
    private RedissonKit() {
        client = Validate.notNull(SpringUtil.getBean(RedissonClient.class), "RedissonClient must not be null.");
    }

    /**
     * 懒汉构造方式，根据Redisson客户端配置，构建Redisson客户端
     */
    private RedissonKit(Config config) {
        client = Redisson.create(config);
    }

    ///////////////////////////////// 工具类提供共有方法 ////////////////////////////////////////////////

    /**
     * 懒汉双重锁的方式，获取当前单实例
     *
     * @return RedissonKit
     */
    public static RedissonKit getInstance() {
        return getInstance(null);
    }

    /**
     * 懒汉双重锁的方式，获取当前单实例
     *
     * @return RedissonKit
     */
    public static RedissonKit getInstance(Config config) {
        if (instance == null) {
            synchronized (RedissonKit.class) {
                if (instance == null) {
                    instance = isNotNull(config) ? new RedissonKit(config) : new RedissonKit();
                }
            }
        }
        return instance;
    }

    /**
     * 根据spring boot的redis配置，创建单击模式的Redisson客户端配置
     *
     * @param properties spring boot的redis配置
     * @return Config Redisson客户端配置
     */
    @SuppressWarnings("unused")
    public static Config createConfig(RedisProperties properties,RedisExtendProperties redisExtendProperties) {
        return createConfig(properties,redisExtendProperties, RedisMode.SINGLE);
    }


    /**
     * 根据spring boot的redis配置，redis模式，创建Redisson客户端配置
     *
     * @param properties spring boot的redis配置
     * @param redisMode  redis模式
     * @return Config Redisson客户端配置
     */
    public static Config createConfig(RedisProperties properties,RedisExtendProperties redisExtendProperties,String redisMode) {
        // 哨兵模式
        if (ObjectUtil.equals(redisMode, RedisMode.SENTINEL)) {
            return createSentinelConfig(properties,redisExtendProperties);
        }
        // 集群模式
        if (ObjectUtil.equals(redisMode, RedisMode.CLUSTER)) {
            return createClusterConfig(properties,redisExtendProperties);
        }
        // 单机模式
        return createSingleConfig(properties,redisExtendProperties);
    }

    /**
     * 创建单机模式的Redisson客户端
     *
     * @param properties spring-boot的redis配置
     * @return Config
     */
    public static Config createSingleConfig(RedisProperties properties,RedisExtendProperties redisExtendProperties) {
        // 构建config对象
        Config config = new Config();
        // 指定配置使用单机模式
        SingleServerConfig baseConfig = config.useSingleServer()
                // 设置redis库
                .setDatabase(properties.getDatabase())
                // 设置客户端连接地址
                .setAddress(buildAddress(properties,redisExtendProperties))
                // 设置密码
                .setPassword(getPassword(properties))
                //设置用户名
                .setUsername(geUserName(properties))
                .setSslEnableEndpointIdentification(properties.isSsl())
                // 设置超时时间
                .setTimeout(getTimeout(properties));

        baseConfig.setConnectionPoolSize(redisExtendProperties.getConnectionPoolSize());
        baseConfig.setConnectionMinimumIdleSize(redisExtendProperties.getConnectionMinimumIdleSize());
        baseConfig.setIdleConnectionTimeout(redisExtendProperties.getIdleConnectionTimeout());
        baseConfig.setConnectTimeout(redisExtendProperties.getConnectTimeout());
        baseConfig.setTimeout(redisExtendProperties.getTimeout());
        baseConfig.setPingConnectionInterval(redisExtendProperties.getPingConnectionInterval());

        baseConfig.setTcpKeepAliveCount(redisExtendProperties.getTcpKeepAliveCount());           // 最多发送3次保活探测包
        baseConfig.setTcpKeepAliveIdle(redisExtendProperties.getTcpKeepAliveIdle());          // 连接空闲2分钟后开始保活探测
        baseConfig.setTcpKeepAliveInterval(redisExtendProperties.getTcpKeepAliveInterval());       // 保活探测间隔30秒
        baseConfig.setTcpUserTimeout(redisExtendProperties.getTcpUserTimeout());         // 数据包超时8秒
        // 返回单机模式的配置对象
        return config;
    }



    /**
     * 创建集群模式的Redisson客户端
     *
     * @param properties spring-boot的redis配置
     * @return Config
     */
    public static Config createClusterConfig(RedisProperties properties,RedisExtendProperties redisExtendProperties) {
        // 构建config对象
        Config config = new Config();
        // 指定配置使用集群模式
        ClusterServersConfig baseConfig = config.useClusterServers()
                // 添加集群节点连接地址
                .addNodeAddress(buildAddress(properties.getCluster().getNodes(),redisExtendProperties.getAddressPrefix()))
                //设置用户名
                .setUsername(geUserName(properties))
                // 设置密码
                .setPassword(getPassword(properties))
                .setSslEnableEndpointIdentification(properties.isSsl())
                // 设置超时时间
                .setTimeout(getTimeout(properties));

        baseConfig.setMasterConnectionPoolSize(redisExtendProperties.getConnectionPoolSize());
        baseConfig.setSlaveConnectionPoolSize(redisExtendProperties.getConnectionPoolSize());
        baseConfig.setMasterConnectionMinimumIdleSize(redisExtendProperties.getConnectionMinimumIdleSize());
        baseConfig.setSlaveConnectionMinimumIdleSize(redisExtendProperties.getConnectionMinimumIdleSize());
        baseConfig.setIdleConnectionTimeout(redisExtendProperties.getIdleConnectionTimeout());
        baseConfig.setConnectTimeout(redisExtendProperties.getConnectTimeout());
        baseConfig.setTimeout(redisExtendProperties.getTimeout());
        baseConfig.setPingConnectionInterval(redisExtendProperties.getPingConnectionInterval());

        baseConfig.setTcpKeepAliveCount(redisExtendProperties.getTcpKeepAliveCount());           // 最多发送3次保活探测包
        baseConfig.setTcpKeepAliveIdle(redisExtendProperties.getTcpKeepAliveIdle());          // 连接空闲2分钟后开始保活探测
        baseConfig.setTcpKeepAliveInterval(redisExtendProperties.getTcpKeepAliveInterval());       // 保活探测间隔30秒
        baseConfig.setTcpUserTimeout(redisExtendProperties.getTcpUserTimeout());         // 数据包超时8秒
        // 返回集群模式的配置对象
        return config;
    }


    /**
     * 创建哨兵模式的Redisson客户端
     *
     * @param properties spring-boot的redis配置
     * @return Config
     */
    private static Config createSentinelConfig(RedisProperties properties,RedisExtendProperties redisExtendProperties) {
        // 构建config对象
        Config config = new Config();
        // 指定配置使用哨兵模式
        SentinelServersConfig baseConfig = config.useSentinelServers()
                // 设置主节点名称
                .setMasterName(properties.getSentinel().getMaster())
                // 添加哨兵节点连接地址
                .addSentinelAddress(buildAddress(properties.getSentinel().getNodes(),redisExtendProperties.getAddressPrefix()))
                //设置用户名
                .setUsername(geUserName(properties))
                // 设置密码
                .setPassword(getPassword(properties))
                .setSslEnableEndpointIdentification(properties.isSsl())
                // 设置超时时间
                .setTimeout(getTimeout(properties));

        baseConfig.setMasterConnectionPoolSize(redisExtendProperties.getConnectionPoolSize());
        baseConfig.setSlaveConnectionPoolSize(redisExtendProperties.getConnectionPoolSize());
        baseConfig.setMasterConnectionMinimumIdleSize(redisExtendProperties.getConnectionMinimumIdleSize());
        baseConfig.setSlaveConnectionMinimumIdleSize(redisExtendProperties.getConnectionMinimumIdleSize());
        baseConfig.setIdleConnectionTimeout(redisExtendProperties.getIdleConnectionTimeout());
        baseConfig.setConnectTimeout(redisExtendProperties.getConnectTimeout());
        baseConfig.setTimeout(redisExtendProperties.getTimeout());
        baseConfig.setPingConnectionInterval(redisExtendProperties.getPingConnectionInterval());

        baseConfig.setTcpKeepAliveCount(redisExtendProperties.getTcpKeepAliveCount());           // 最多发送3次保活探测包
        baseConfig.setTcpKeepAliveIdle(redisExtendProperties.getTcpKeepAliveIdle());          // 连接空闲2分钟后开始保活探测
        baseConfig.setTcpKeepAliveInterval(redisExtendProperties.getTcpKeepAliveInterval());       // 保活探测间隔30秒
        baseConfig.setTcpUserTimeout(redisExtendProperties.getTcpUserTimeout());         // 数据包超时8秒
        // 返回哨兵模式的配置对象
        return config;
    }

    ///////////////////////////////// 工具类【实例】提供共有方法 ////////////////////////////////////////////////

    /**
     * 提供获取redis锁客户端的方法
     *
     * @return RedissonClient
     */
    @SuppressWarnings("unused")
    public RedissonClient getClient() {
        return client;
    }

    /**
     * 加锁操作，线程会一直等待 直到拿到该锁
     *
     * @param name     锁名称
     * @param consumer 业务功能消费对象
     */
    @SuppressWarnings("unused")
    public void lock(String name, Consumer<RLock> consumer) {
        lock(name, consumer, null);
    }

    /**
     * 加锁操作，线程会一直等待 直到拿到该锁
     * <p>
     * 该函数接收一个指定的异常消费函数，可以自定义处理异常
     *
     * @param name     锁名称
     * @param consumer 业务功能消费对象
     * @param unusual  异常消费对象
     */
    public void lock(String name, Consumer<RLock> consumer, Consumer<Exception> unusual) {
        lock(name, -1, consumer, unusual);
    }

    /**
     * 加锁操作，线程会一直等待 直到拿到该锁，拿到锁以后，会在指定过期时间到的时候自动释放锁
     *
     * @param name     锁名称
     * @param expire   锁过期时间，单位：秒
     * @param consumer 业务功能消费对象
     */
    @SuppressWarnings("unused")
    public void lock(String name, int expire, Consumer<RLock> consumer) {
        lock(name, expire, consumer, null);
    }

    /**
     * 加锁操作，线程会一直等待 直到拿到该锁，拿到锁以后，会在指定过期时间到的时候自动释放锁
     * <p>
     * 该函数接收一个指定的异常消费函数，可以自定义处理异常
     *
     * @param name     锁名称
     * @param expire   锁过期时间，单位：秒
     * @param consumer 业务功能消费对象
     * @param unusual  异常消费对象
     */
    public void lock(String name, int expire, Consumer<RLock> consumer, Consumer<Exception> unusual) {
        //1. 根据锁名称，获取锁
        RLock lock = client.getLock(LOCK_PREFIX.concat(name));
        try {
            //2. 执行加锁操作，并设置过期时间
            lock.lock(expire, TimeUnit.SECONDS);
            if (log.isDebugEnabled()) {
                log.debug("------>> 执行业务[{}]<<加锁>>成功，expire={}, isLocked={}, holdCount={}, heldByCurrentThread={}, remainTimeToLive={}",
                        name, expire, lock.isLocked(), lock.getHoldCount(), lock.isHeldByCurrentThread(), lock.remainTimeToLive());
            }
            //3. 执行业务操作
            consumer.accept(lock);
        } catch (Exception e) {
            //4. 打印异常日志，如果异常消费函数为空，直接抛出异常
            log.error("执行业务[" + name + "]<<加锁>>异常：" + e.getMessage(), e);
            if (isNull(unusual)) {
                throw e;
            }
            //5. 否则，执行异常消费函数
            unusual.accept(e);
        } finally {
            //6. 释放锁
            unlock(name, lock, expire);
        }
    }

    /**
     * 尝试加锁操作，线程如果再等待时间内，加锁失败，会抛{@link TryLockException}
     *
     * @param name     锁名称
     * @param wait     线程最大等待锁时间
     * @param consumer 业务功能消费对象
     */
    @SuppressWarnings("unused")
    public void tryLock(String name, int wait, Consumer<RLock> consumer) {
        tryLock(name, wait, consumer, null);
    }

    /**
     * 尝试加锁操作，线程如果再等待时间内，加锁失败，会抛{@link TryLockException}
     * <p>
     * 该函数接收一个指定的异常消费函数，可以自定义处理异常
     *
     * @param name     锁名称
     * @param wait     线程最大等待锁时间
     * @param consumer 业务功能消费对象
     * @param unusual  异常消费对象
     */
    public void tryLock(String name, int wait, Consumer<RLock> consumer, Consumer<Exception> unusual) {
        tryLock(name, wait, -1, consumer, unusual);
    }

    /**
     * 尝试加锁操作，线程如果再等待时间内，加锁失败，会抛{@link TryLockException}，如果加锁成功，会在指定过期时间到的时候自动释放锁
     *
     * @param name     锁名称
     * @param wait     线程最大等待锁时间
     * @param expire   锁过期时间，单位：秒
     * @param consumer 业务功能消费对象
     */
    @SuppressWarnings("unused")
    public void tryLock(String name, int wait, int expire, Consumer<RLock> consumer) {
        tryLock(name, wait, expire, consumer, null);
    }

    /**
     * 尝试加锁操作，线程如果再等待时间内，加锁失败，会抛{@link TryLockException}，如果加锁成功，会在指定过期时间到的时候自动释放锁
     * <p>
     * 该函数接收一个指定的异常消费函数，可以自定义处理异常
     *
     * @param name     锁名称
     * @param wait     线程最大等待锁时间
     * @param expire   锁过期时间，单位：秒
     * @param consumer 业务功能消费对象
     * @param unusual  异常消费对象
     */
    public void tryLock(String name, int wait, int expire, Consumer<RLock> consumer, Consumer<Exception> unusual) {
        //1. 根据锁名称，获取锁
        RLock lock = client.getLock(LOCK_PREFIX.concat(name));
        try {
            //2. 尝试加锁成功
            if (lock.tryLock(wait, TimeUnit.SECONDS)) {
                if (log.isDebugEnabled()) {
                    log.debug("------>> 执行业务[{}]<<尝试加锁>>成功，wait={}, expire={}, isLocked={}, holdCount={}, heldByCurrentThread={}, remainTimeToLive={}",
                            name, wait, expire, lock.isLocked(), lock.getHoldCount(), lock.isHeldByCurrentThread(), lock.remainTimeToLive());
                }
                //3. 执行业务操作
                consumer.accept(lock);
            }
            //4. 尝试加锁失败，打印异常，并抛出异常
            else {
                log.error("------>> 执行业务[{}]<<尝试加锁>>失败，wait={}, expire={}, isLocked={}, holdCount={}, heldByCurrentThread={}, remainTimeToLive={}",
                        name, wait, expire, lock.isLocked(), lock.getHoldCount(), lock.isHeldByCurrentThread(), lock.remainTimeToLive());
                throw new TryLockException("获取业务[" + name + "]的锁失败，请稍后再试！");
            }
        } catch (Exception e) {
            //5. 打印异常日志，如果异常消费函数为空，直接抛出异常
            log.error("执行业务[" + name + "]<<尝试加锁>>异常：" + e.getMessage(), e);
            if (isNull(unusual)) {
                throw ExceptionUtil.wrapUnchecked(e);
            }
            //6. 否则，执行异常消费函数
            unusual.accept(e);
        } finally {
            //7. 释放锁
            unlock(name, lock, expire);
        }
    }

    /**
     * 解锁操作
     *
     * @param name 锁名称
     */
    @SuppressWarnings("unused")
    public void unlock(String name) {
        unlock(name, client.getLock(name));
    }

    /**
     * 解锁操作
     *
     * @param name 锁名称
     * @param lock redis锁
     */
    public void unlock(String name, RLock lock) {
        unlock(name, lock, null);
    }


    /**
     * 解锁操作
     *
     * @param name   锁名称
     * @param lock   redis锁
     * @param expire 锁过期时间，单位：秒
     */
    public void unlock(String name, RLock lock, Integer expire) {
        //释放锁，参数锁不为空，且锁为锁定状态
        if (isNotNull(lock) && lock.isLocked()) {
            //并且锁为当前线程持有时，才执行释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                if (log.isDebugEnabled()) {
                    log.debug("------>> 执行业务[{}]（解锁）完成，expire={}，isLocked={}, holdCount={}, heldByCurrentThread={}, remainTimeToLive={}",
                            name, expire, lock.isLocked(), lock.getHoldCount(), lock.isHeldByCurrentThread(), lock.remainTimeToLive());
                }
            }
            //打印日志
            else {
                if (log.isDebugEnabled()) {
                    log.debug("------>> 非当前线程持有锁的情况下，不能执行业务[{}]（解锁）操作，expire={}，isLocked={}, holdCount={}, heldByCurrentThread={}, remainTimeToLive={}",
                            name, expire, lock.isLocked(), lock.getHoldCount(), lock.isHeldByCurrentThread(), lock.remainTimeToLive());
                }
            }
            return;
        }
        //已经被释放的情况下，打印日志
        if (log.isDebugEnabled()) {
            log.debug("------>> 当前业务[{}]锁已被释放，expire={}，isLocked={}, holdCount={}, heldByCurrentThread={}, remainTimeToLive={}",
                    name, expire, lock.isLocked(), lock.getHoldCount(), lock.isHeldByCurrentThread(), lock.remainTimeToLive());
        }
    }

    ///////////////////////////////// 私有方法 ////////////////////////////////////////////////

    /**
     * 根据spring boot的redis配置，构建客户端连接地址
     */
    private static String buildAddress(RedisProperties properties,RedisExtendProperties redisExtendProperties) {
        return redisExtendProperties.getAddressPrefix().concat(properties.getHost()).concat(StringPool.COLON).concat(StringUtil.toString(properties.getPort()));
    }

    /**
     * 根据spring boot的redis配置的节点集合，构建客户端连接地址集合
     */
    private static String[] buildAddress(List<String> nodes,String addressPrefix) {
        return nodes.stream().map(addressPrefix::concat).toArray(String[]::new);
    }

    /**
     * 根据spring boot的redis配置，获取客户端连接密码，为空的情况下，返回null
     */
    private static String getPassword(RedisProperties properties) {
        return isNotEmpty(properties.getPassword()) ? properties.getPassword() : null;
    }

    /**
     * 根据spring boot的redis配置，获取客户端连接密码，为空的情况下，返回null
     */
    private static String geUserName(RedisProperties properties) {
        return isNotEmpty(properties.getUsername()) ? properties.getUsername() : null;
    }

    /**
     * 根据spring boot的redis配置，获取客户端连接超时时间，超时配置为空的情况下，返回默认值3000 ms
     */
    private static int getTimeout(RedisProperties properties) {
        return isNotNull(properties.getTimeout()) ? (int) properties.getTimeout().toMillis() : 3000;
    }

}

