package cn.need.framework.common.dict.impl;

import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.dict.api.ExceptionLangRepertory;
import cn.need.framework.common.dict.constant.DictConstant;
import cn.need.framework.common.dict.util.DictAttributeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */

@Slf4j
public class RedisExceptionLangRepertory implements ExceptionLangRepertory {

    private static final String DEFAULT_EXCEPTION_MESSAGE = "Data operation exception";
    /**
     * redisTemplate对象，需要注入
     */
    private final RedisTemplate<String, String> redisTemplate;
    /**
     * 语言数据在redis中的key值，可以注入
     */
    private final String redisKey;

    public RedisExceptionLangRepertory(RedisTemplate<String, String> redisTemplate, String redisKey) {
        this.redisTemplate = redisTemplate;
        this.redisKey = isEmpty(redisKey) ? DictConstant.LANG_FIELD_EXCEPTION_EKY : DictConstant.LANG_FIELD_EXCEPTION_EKY.concat(StringPool.COLON).concat(redisKey);
    }

    public RedisExceptionLangRepertory(RedisTemplate<String, String> redisTemplate) {
        this(redisTemplate, null);
    }

    private static String getLangByRedisKey(String key) {
        if (isEmpty(key)) {
            return null;
        }
        List<String> strList = Arrays.asList(key.split(StringPool.COLON));
        if (strList.size() <= 2) {
            return null;
        }
        return strList.get(strList.size() - 1);
    }

    @Override
    public void clear() {
        TimeMeter meter = new TimeMeter();
        String pattern = this.redisKey.concat(StringPool.COLON).concat(StringPool.ASTERISK);
        Set<String> keys = nullToDefault(DictAttributeUtil.scanKeys(redisTemplate,pattern), Lists.hashSet());
        keys.forEach(it -> {
            if (log.isDebugEnabled()) {
                log.debug("----------->> 删除系统多语言：{}", it);
            }
            if (isNotEmpty(it)) {
                redisTemplate.delete(it);
            }
        });
        if (log.isDebugEnabled()) {
            log.debug("----------->> <<<清空>>>所有【系统多语言】数据完成！分组数量：{}，用时：{}ms", keys, meter.sign());
        }
    }

    @Override
    public void initialization(Map<String, Map<String, String>> langFieldMap) {
        TimeMeter meter = new TimeMeter();
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化缓存【系统多语言配置】数据【开始】，初始化分组数量：{} ============", isNotNull(langFieldMap) ? langFieldMap.size() : 0);
        }
        //清空缓存数据
        clear();
        //将数据保存至redis
        if (isNotNull(langFieldMap)) {
            langFieldMap.forEach((key, map) -> {
                TimeMeter subMeter = new TimeMeter();
                add(map, key);
                if (log.isDebugEnabled()) {
                    log.debug("----------- 添加系统多语言配置至redis，key：{}，数量：{}，耗时{}ms ============", key, map.size(), subMeter.sign());
                }
            });
        }
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化缓存【系统多语言配置】数据<<<完成>>>，总耗时{}ms ============", meter.sign());
        }
    }

    @Override
    public BoundHashOperations<String, String, String> getHash(String lang) {
        return this.redisTemplate.boundHashOps(buildHashKey(lang));
    }

    @Override
    public void add(String key, String value, String lang) {
        if (isEmpty(key)) {
            return;
        }
        getHash(lang).put(key, nullToDefault(value, StringPool.EMPTY));
    }

    @Override
    public void del(String key, String lang) {
        if (isEmpty(key)) {
            return;
        }
        BoundHashOperations<String, String, String> langFiledHash = getHash(lang);
        if (equal(langFiledHash.hasKey(key), Boolean.TRUE)) {
            langFiledHash.delete(key);
        }
    }

    @Override
    public void add(Map<String, String> langFieldMap, String lang) {
        if (isEmpty(langFieldMap)) {
            return;
        }
        getHash(lang).putAll(langFieldMap);
    }

    @Override
    public Map<String, String> listLand(String lang) {
        return nullToDefault(getHash(lang).entries(), Maps.hashMap());
    }

    @Override
    public void delLang(String lang) {
        this.redisTemplate.delete(this.redisKey.concat(StringPool.COLON).concat(StringUtil.emptyToDefault(lang, "zh")));
    }

    @Override
    public Map<String, Map<String, String>> allLand() {
        Map<String, Map<String, String>> result = Maps.hashMap();
        TimeMeter meter = new TimeMeter();

        String pattern = this.redisKey.concat(StringPool.COLON).concat(StringPool.ASTERISK);
        Set<String> keys = nullToDefault(DictAttributeUtil.scanKeys(redisTemplate,pattern), Lists.hashSet());
        if (log.isDebugEnabled()) {
            log.debug("----------->> 获取缓存中的多语言编码，用时：{}ms", keys, meter.sign());
        }
        keys.forEach(it -> {
            String lang = getLangByRedisKey(it);
            if (isEmpty(lang)) {
                return;
            }
            Map<String, String> map = this.listLand(lang);
            result.put(it, map);
        });
        if (log.isDebugEnabled()) {
            log.debug("----------->> 获取所有多语言配置语言：{}，用时：{}ms", keys, meter.sign());
        }
        return result;
    }

    @Override
    public Map<String, Map<String, String>> listByMulLang(List<String> langList) {
        if (isEmpty(langList)) {
            return Maps.hashMap();
        }
        Map<String, Map<String, String>> result = Maps.hashMap();
        langList.forEach(it -> {
            Map<String, String> map = this.listLand(it);
            result.put(it, map);
        });
        return result;
    }

    @Override
    public String get(String key, String lang) {
        String language = isEmpty(lang) ? isEmpty(Users.language()) ? DictConstant.DEFAULT_LANG : Users.language() : lang;
        log.debug("----------->> 获取系统多语言：{}，语言：{}", key, lang);
        return isEmpty(key) ? DEFAULT_EXCEPTION_MESSAGE : nullToDefault(getHash(language).get(key), DEFAULT_EXCEPTION_MESSAGE);
    }

    @Override
    public String get(String key, String lang, Object... arguments) {
        String language = isEmpty(lang) ? (isEmpty(Users.language()) ? DictConstant.DEFAULT_LANG : Users.language()) : lang;
        log.debug("----------->> 获取系统多语言：{}，语言：{}", key, lang);

        if (isEmpty(key)) {
            return "Incorrect configuration information";
        } else {
            String messageTemplate = nullToDefault(getHash(language).get(key), StringPool.EMPTY);

            // 使用MessageFormat替换占位符
            // "A demand form ( {0} ) has been submitted. Please review and approve promptly!";
            return ObjectUtil.emptyToDefault(MessageFormat.format(messageTemplate, arguments), DEFAULT_EXCEPTION_MESSAGE);
        }
    }

    /**
     * 根级语言值获取redisTemplate的hashKey值
     */
    private String buildHashKey(String lang) {
        lang = StringUtil.emptyToDefault(lang, DictConstant.DEFAULT_LANG);
        return this.redisKey.concat(StringPool.COLON).concat(lang);
    }
}
