package cn.need.framework.common.dict.api;

import cn.need.framework.common.dict.entity.Dict;
import org.springframework.data.redis.core.BoundHashOperations;

import java.util.Collection;
import java.util.List;

/**
 * 字典缓存操作接口，包含了在缓存中对字典数据进行增、删、改、查等操作
 *
 * <AUTHOR>
 */
public interface DictRepertory {

    /**
     * 根据字典的code获取单条数据字典记录
     *
     * @param code 字典编号
     * @return Dict 数据字典对象
     */
    Dict get(String code);

    /**
     * 根据字典编号、根级编号获取单条数据字典记录
     *
     * @param code 字典编号
     * @param root 根级编号
     * @return Dict 数据字典对象
     * @since 1.3.0
     */
    Dict get(String code, String root);

    /**
     * 根据字典编号集合获取其对应的字典记录集合
     *
     * @param codes 字典编号集合
     * @return List<Dict> 数据字典集合
     */
    List<Dict> list(Collection<String> codes);

    /**
     * 根据字典编号集合、根级编号获取其对应的字典记录集合
     *
     * @param codes 字典编号集合
     * @param root  根级编号
     * @return List<Dict> 数据字典集合
     * @since 1.3.0
     */
    List<Dict> list(Collection<String> codes, String root);

    /**
     * 根据根级编号获取其对应的字典记录集合
     *
     * @param root 根级编号
     * @return List<Dict> 数据字典集合
     * @since 1.3.0
     */
    List<Dict> list(String root);

    /**
     * 根据字典编号集合获取其对应的直系子集字典集合，会按照字典sorting值对集合进行排序
     *
     * @param code 字典编号
     * @return List<Dict> 字典编号映射的直系子集字典集合
     */
    List<Dict> subList(String code);

    /**
     * 根据字典编号集合、根级编号获取其对应的直系子集字典集合，会按照字典sorting值对集合进行排序
     *
     * @param code 字典编号
     * @param root 根级编号
     * @return List<Dict> 字典编号映射的直系子集字典集合
     */
    List<Dict> subList(String code, String root);

    /**
     * 新增数据字典
     * <p>
     * 该方法接收一个封装后的数据字典对象，并将该字典数据加入到缓存中
     *
     * @param bean 数据字典对象
     */
    void add(Dict bean);

    /**
     * 批量新增数据字典
     * <p>
     * 该方法接收一个封装后的数据字典对象集合，并将集合中的字典数据加入到缓存中
     *
     * @param beans 数据字典集合
     */
    void add(Collection<Dict> beans);

    /**
     * 根据字典编号删除其对应的字典数据及其子集
     *
     * @param code 字典编号
     */
    void del(String code);

    /**
     * 根据字典编号、根级编号删除其对应的字典数据及其子集
     *
     * @param code 字典编号
     * @param root 根级编号
     * @since 1.3.0
     */
    void del(String code, String root);

    /**
     * 根据字典编号删除其对应的字典数据及其子集
     *
     * @param codes 字典编号
     * @param root  字典编号
     */
    void del(Collection<String> codes, String root);

    /**
     * 根据字典对象删除其对应的字典数据及其子集
     *
     * @param bean 字典对象
     * @since 1.3.0
     */
    void del(Dict bean);

    /**
     * 根据字典对象集合删除其对应的字典数据及其子集
     *
     * @param beans 字典对象集合
     * @since 1.3.0
     */
    void del(Collection<Dict> beans);

    /**
     * 清空所有字典数据
     *
     * @since 1.1.0
     */
    void clear();

    /**
     * 初始化缓存中的字典数据
     * <p>
     * 该方法会做俩件事：
     * <p>
     * 1.先清空所有字典的缓存数据
     * <p>
     * 2.将字典数据集合添加到缓存中
     *
     * @param beans 数据字典集合
     */
    void initialization(Collection<Dict> beans);

    /**
     * 获取RedisTemplate的BoundHashOperations对象
     *
     * @param root 值集
     * @return BoundHashOperations<String, String, Dict>
     * @since 1.3.0
     */
    BoundHashOperations<String, String, Dict> getHash(String root);

}
