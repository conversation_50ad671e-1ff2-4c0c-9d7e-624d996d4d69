package cn.need.framework.common.core.trace;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 链路追踪日志工具类
 * 
 * 提供带TraceId的日志输出功能，确保所有业务日志都包含链路追踪信息
 * 使用方式：
 * 1. 直接使用静态方法输出日志
 * 2. 获取增强的Logger实例
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public class TraceLogger {
    
    /**
     * 获取带TraceId增强的Logger
     * 
     * @param clazz 日志类
     * @return 增强的Logger
     */
    public static TraceLoggerWrapper getLogger(Class<?> clazz) {
        return new TraceLoggerWrapper(LoggerFactory.getLogger(clazz));
    }
    
    /**
     * 获取带TraceId增强的Logger
     * 
     * @param name 日志名称
     * @return 增强的Logger
     */
    public static TraceLoggerWrapper getLogger(String name) {
        return new TraceLoggerWrapper(LoggerFactory.getLogger(name));
    }
    
    /**
     * 输出INFO级别日志
     * 
     * @param logger 日志对象
     * @param message 日志消息
     * @param args 参数
     */
    public static void info(Logger logger, String message, Object... args) {
        if (logger.isInfoEnabled()) {
            logger.info(formatMessage(message), args);
        }
    }
    
    /**
     * 输出DEBUG级别日志
     * 
     * @param logger 日志对象
     * @param message 日志消息
     * @param args 参数
     */
    public static void debug(Logger logger, String message, Object... args) {
        if (logger.isDebugEnabled()) {
            logger.debug(formatMessage(message), args);
        }
    }
    
    /**
     * 输出WARN级别日志
     * 
     * @param logger 日志对象
     * @param message 日志消息
     * @param args 参数
     */
    public static void warn(Logger logger, String message, Object... args) {
        if (logger.isWarnEnabled()) {
            logger.warn(formatMessage(message), args);
        }
    }
    
    /**
     * 输出ERROR级别日志
     * 
     * @param logger 日志对象
     * @param message 日志消息
     * @param args 参数
     */
    public static void error(Logger logger, String message, Object... args) {
        if (logger.isErrorEnabled()) {
            logger.error(formatMessage(message), args);
        }
    }
    
    /**
     * 输出ERROR级别日志（带异常）
     * 
     * @param logger 日志对象
     * @param message 日志消息
     * @param throwable 异常
     */
    public static void error(Logger logger, String message, Throwable throwable) {
        if (logger.isErrorEnabled()) {
            logger.error(formatMessage(message), throwable);
        }
    }
    
    /**
     * 格式化日志消息，添加TraceId信息
     * 
     * @param message 原始消息
     * @return 格式化后的消息
     */
    private static String formatMessage(String message) {
        String traceInfo = TraceContext.getTraceInfo();
        return traceInfo + " " + message;
    }
    
    /**
     * TraceLogger包装器
     */
    public static class TraceLoggerWrapper {
        private final Logger logger;
        
        public TraceLoggerWrapper(Logger logger) {
            this.logger = logger;
        }
        
        public void info(String message, Object... args) {
            TraceLogger.info(logger, message, args);
        }
        
        public void debug(String message, Object... args) {
            TraceLogger.debug(logger, message, args);
        }
        
        public void warn(String message, Object... args) {
            TraceLogger.warn(logger, message, args);
        }
        
        public void error(String message, Object... args) {
            TraceLogger.error(logger, message, args);
        }
        
        public void error(String message, Throwable throwable) {
            TraceLogger.error(logger, message, throwable);
        }
        
        public boolean isInfoEnabled() {
            return logger.isInfoEnabled();
        }
        
        public boolean isDebugEnabled() {
            return logger.isDebugEnabled();
        }
        
        public boolean isWarnEnabled() {
            return logger.isWarnEnabled();
        }
        
        public boolean isErrorEnabled() {
            return logger.isErrorEnabled();
        }
        
        /**
         * 获取原始Logger（用于特殊情况）
         * 
         * @return 原始Logger
         */
        public Logger getOriginalLogger() {
            return logger;
        }
    }
}
