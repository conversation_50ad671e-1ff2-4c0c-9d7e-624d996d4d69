package cn.need.framework.common.core.trace;

import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * 链路追踪上下文工具类
 * 
 * 提供统一的TraceId管理功能，支持：
 * 1. TraceId的获取和设置
 * 2. 调用链路层级关系管理
 * 3. 业务日志的TraceId注入
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public class TraceContext {
    
    private static final String TRACE_ID = "traceId";
    private static final String SPAN_SEPARATOR = ".";
    
    /**
     * 获取当前线程的TraceId
     * 
     * @return TraceId，如果不存在则返回空字符串
     */
    public static String getTraceId() {
        String traceId = MDC.get(TRACE_ID);
        return traceId != null ? traceId : "";
    }
    
    /**
     * 设置当前线程的TraceId
     * 
     * @param traceId TraceId
     */
    public static void setTraceId(String traceId) {
        if (StringUtils.hasText(traceId)) {
            MDC.put(TRACE_ID, traceId);
        }
    }
    
    /**
     * 清除当前线程的TraceId
     */
    public static void clearTraceId() {
        MDC.remove(TRACE_ID);
    }
    
    /**
     * 检查是否存在TraceId
     * 
     * @return true如果存在TraceId
     */
    public static boolean hasTraceId() {
        return StringUtils.hasText(getTraceId());
    }
    
    /**
     * 生成新的子调用TraceId
     * 在当前TraceId基础上添加新的span层级
     * 
     * @param serviceName 服务名称标识
     * @return 新的TraceId
     */
    public static String generateChildTraceId(String serviceName) {
        String currentTraceId = getTraceId();
        String spanId = generateSpanId();
        
        if (StringUtils.hasText(currentTraceId)) {
            return currentTraceId + SPAN_SEPARATOR + serviceName + "-" + spanId;
        } else {
            return "ROOT-" + serviceName + "-" + spanId;
        }
    }
    
    /**
     * 执行带TraceId的操作
     * 自动管理TraceId的设置和清理
     * 
     * @param traceId TraceId
     * @param runnable 要执行的操作
     */
    public static void runWithTraceId(String traceId, Runnable runnable) {
        String originalTraceId = getTraceId();
        try {
            setTraceId(traceId);
            runnable.run();
        } finally {
            if (StringUtils.hasText(originalTraceId)) {
                setTraceId(originalTraceId);
            } else {
                clearTraceId();
            }
        }
    }
    
    /**
     * 执行带TraceId的操作（带返回值）
     * 自动管理TraceId的设置和清理
     * 
     * @param traceId TraceId
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T callWithTraceId(String traceId, java.util.function.Supplier<T> supplier) {
        String originalTraceId = getTraceId();
        try {
            setTraceId(traceId);
            return supplier.get();
        } finally {
            if (StringUtils.hasText(originalTraceId)) {
                setTraceId(originalTraceId);
            } else {
                clearTraceId();
            }
        }
    }
    
    /**
     * 获取TraceId信息用于日志输出
     * 
     * @return 格式化的TraceId信息
     */
    public static String getTraceInfo() {
        String traceId = getTraceId();
        return StringUtils.hasText(traceId) ? "[TraceId: " + traceId + "]" : "[TraceId: NO_TRACE]";
    }
    
    /**
     * 生成SpanId
     * 
     * @return 8位随机字符串
     */
    private static String generateSpanId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 获取当前调用链路的层级深度
     * 
     * @return 层级深度，根节点为0
     */
    public static int getTraceDepth() {
        String traceId = getTraceId();
        if (!StringUtils.hasText(traceId)) {
            return 0;
        }
        
        // 计算分隔符的数量来确定层级深度
        int depth = 0;
        int index = 0;
        while ((index = traceId.indexOf(SPAN_SEPARATOR, index)) != -1) {
            depth++;
            index++;
        }
        return depth;
    }
    
    /**
     * 获取根TraceId（去除所有span信息）
     * 
     * @return 根TraceId
     */
    public static String getRootTraceId() {
        String traceId = getTraceId();
        if (!StringUtils.hasText(traceId)) {
            return "";
        }
        
        int firstSeparatorIndex = traceId.indexOf(SPAN_SEPARATOR);
        if (firstSeparatorIndex > 0) {
            return traceId.substring(0, firstSeparatorIndex);
        }
        return traceId;
    }
}
