package cn.need.framework.common.oss.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * Minio参数配置类
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "oss")
public class OssProperties {

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 对象存储名称
     */
    private String name;

    /**
     * 对象存储服务的URL
     */
    private String endpoint;

    /**
     * 应用ID TencentCOS需要
     */
    private String appId;

    /**
     * 区域简称 TencentCOS需要
     * 如果是本地文件上传需要 如 product
     */
    private String region;

    /**
     * Access key就像用户ID，可以唯一标识你的账户
     */
    private String accessKey;

    /**
     * Secret key是你账户的密码
     */
    private String secretKey;

    /**
     * 默认的存储桶名称
     * 如果是本地文件上传 刚为:/opt/html/product/
     */
    private String bucketName;


    /**
     * 过期时间 毫秒
     * AliossTemplate用到
     */
    private Long expireTime;

    /**
     * 大小限制
     * 1M*1024*1024
     */
    private Long contentLengthRange;


}
