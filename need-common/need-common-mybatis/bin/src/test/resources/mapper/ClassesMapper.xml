<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.framework.common.mybatis.mapper.ClassesMapper">

    <delete id="delete" parameterType="java.util.List">
        delete from ud_classes where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteAll">
        truncate table ud_classes
    </delete>
</mapper>
