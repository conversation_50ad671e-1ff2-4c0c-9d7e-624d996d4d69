package ${package.Controller};

#if(${superControllerClassPackage})
import ${converterPackage}.${converterName};
import ${package.Entity}.${entity};
import ${voPackage}.${voName};
import ${package.Service}.${table.serviceName};
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import ${superControllerClassPackage};
    #if(${springdoc})
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
    #elseif(${swagger})
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
    #end
import org.springframework.web.bind.annotation.*;
    #if(${controllerImport})
import org.springframework.web.multipart.MultipartFile;
    #end

import javax.validation.Valid;
#else
    #if(${restControllerStyle})
import org.springframework.web.bind.annotation.RestController;
    #else
import org.springframework.stereotype.Controller;
    #end
#end

/**
 * <p>
 * ${table.comment} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${restControllerStyle})
@RestController
#else
@Controller
#end
#set($requestBasePath="#if(${requestPrefix})/${requestPrefix}#end#if(${package.ModuleName})/${controllerMappingModule}#end/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end")
@RequestMapping("${requestBasePath}")
#if(${superControllerClass})
    #if(${springdoc})
@Tag(name = "${table.comment}")
    #elseif(${swagger})
@Api(tags = {"${table.comment}"})
    #end
class ${table.controllerName} : ${superControllerClass}<${table.serviceName}, ${entity}, ${converterName}, ${voName}>() {
    #if(${controllerInsert})

    /**
     * <p>
     * 新增数据，返回数据的主键id
     * </p>
     *
     * @param insertVo 需要新增的数据对象
     * @return 数据的主键id
     */
        #if(${springdoc})
    @Operation(summary = "新增数据接口，返回数据的主键id", description = "接收数据的VO对象，将该对象持久化到数据库中表")
        #elseif(${swagger})
    @ApiOperation(value = "新增数据接口，返回数据的主键id", notes = "接收数据的VO对象，将该对象持久化到数据库中表")
        #end
    @PostMapping(value = "/insert")
    fun insert(
            @Valid @RequestBody #if(${springdoc})@Parameter(description = "数据对象", required = true)#elseif(${swagger})@ApiParam(value = "数据对象", required = true)#end insertVo: ${voName}
    ): Result<Long> {
        return super.insert(insertVo, null)
    }
    #end
    #if(${controllerUpdate})

    /**
     * <p>
     * 修改数据，返回受影响行数
     * </p>
     *
     * @param updateVo 需要修改的数据对象
     * @return 受影响行数
     */
        #if(${springdoc})
    @Operation(summary = "修改数据接口，返回受影响行数", description = "接收数据的VO对象，将该对象持久化到数据库中表")
        #elseif(${swagger})
    @ApiOperation(value = "修改数据接口，返回受影响行数", notes = "接收数据的VO对象，将该对象持久化到数据库中表")
        #end
    @PostMapping(value = "/update")
    fun update(
            @Valid @RequestBody #if(${springdoc})@Parameter(description = "数据对象", required = true)#elseif(${swagger})@ApiParam(value = "数据对象", required = true)#end updateVo: ${voName}
    ): Result<Integer> {
        return super.update(updateVo, null)
    }
    #end
    #if(${controllerRemove})

    /**
     * <p>
     * 根据数据主键id删除数据
     * </p>
     *
     * @param id 数据主键id
     * @return 受影响行数
     */
        #if(${springdoc})
    @Operation(summary = "删除数据接口，返回受影响行数", description = "根据数据主键id，从数据库中删除其对应的数据")
        #elseif(${swagger})
    @ApiOperation(value = "删除数据接口，返回受影响行数", notes = "根据数据主键id，从数据库中删除其对应的数据")
        #end
    @PostMapping(value = "/remove")
    fun remove(
            @RequestBody #if(${springdoc})@Parameter(description = "数据主键id", required = true)#elseif(${swagger})@ApiParam(value = "数据主键id", required = true)#end id: IdCondition
    ): Result<Integer> {
        Validate.notNull(id.getId(), "The id value cannot be null.")
        return super.remove(id.getId(), null)
    }
    #end
    #if(${controllerActive})

    /**
     * <p>
     * 根据数据主键id设置数据的有效性
     * </p>
     *
     * @param id 数据主键id
     * @return 受影响行数
     */
        #if(${springdoc})
    @Operation(summary = "设置数据有效性接口，返回受影响行数", description = "根据数据主键id，设置对应数据的有效性")
        #elseif(${swagger})
    @ApiOperation(value = "设置数据有效性接口，返回受影响行数", notes = "根据数据主键id，设置对应数据的有效性")
        #end
    @PostMapping(value = "/active")
    fun active(
            @RequestBody #if(${springdoc})@Parameter(description = "数据主键id", required = true)#elseif(${swagger})@ApiParam(value = "数据主键id", required = true)#end id: IdCondition
    ): Result<Integer> {
        Validate.notNull(id.getId(), "The id value cannot be null.")
        return super.active(id.getId(), null)
    }
    #end
    #if(${controllerDetail})

    /**
     * <p>
     * 根据数据主键id获取数据的详情
     * </p>
     *
     * @param id 数据主键id
     * @return 数据的详情信息
     */
        #if(${springdoc})
    @Operation(summary = "获取数据详情接口", description = "根据数据主键id，从数据库中获取其对应的数据详情")
        #elseif(${swagger})
    @ApiOperation(value = "获取数据详情接口", notes = "根据数据主键id，从数据库中获取其对应的数据详情")
        #end
    @PostMapping(value = "/detail")
    fun detail(
            @RequestBody #if(${springdoc})@Parameter(description = "数据主键id", required = true)#elseif(${swagger})@ApiParam(value = "数据主键id", required = true)#end id: IdCondition
    ): Result<${voName}> {
        Validate.notNull(id.getId(), "The id value cannot be null.")
        return super.detail(id.getId(), null)
    }
    #end
    #if(${controllerList})

        #if(${springdoc})
    @Operation(summary = "获取${table.comment}分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的${table.comment}列表")
        #elseif(${swagger})
    @ApiOperation(value = "获取${table.comment}分页列表", notes = "根据传入的搜索条件参数，从数据库中获取分页后的${table.comment}列表")
        #end
    @PostMapping(value = "/list")
    fun list(
            @RequestBody #if(${springdoc})@Parameter(description = "搜索条件参数", required = true)#elseif(${swagger})@ApiParam(value = "搜索条件参数", required = true)#end search: PageSearch<${entity}Query>
    ): Result<PageData<${voName}>> {
        return success(resultPage);
    }
    #end

    #if(${controllerImport})

    /**
     * <p>
     * 接收一个excel文件，将数据导入到系统中
     * </p>
     *
     * @param file excel文件
     * @return 返回导入成功的数量
     */
        #if(${springdoc})
    @Operation(summary = "数据导入接口，返回导入成功的数量", description = "该接口接收一个excel文件，将文件中的数据导入到系统中")
        #elseif(${swagger})
    @ApiOperation(value = "数据导入接口，返回导入成功的数量", notes = "该接口接收一个excel文件，将文件中的数据导入到系统中")
        #end
    @PostMapping(value = "/import")
    fun importExcel(
            @RequestParam("file") #if(${springdoc})@Parameter(description = "excel文件", required = true)#elseif(${swagger})@ApiParam(value = "excel文件", required = true)#end file: MultipartFile
    ): Result<Integer> {
        return super.importExcel(file, super.voClass, null)
    }
    #end
    #if(${cfg.controllerExport})

    /**
     * <p>
     * 将全量数据导出至一个excel文件中
     * </p>
     */
        #if(${springdoc})
    @Operation(summary = "数据导出接口", description = "将全量数据导出，并生成一个excel文件")
        #elseif(${swagger})
    @ApiOperation(value = "数据导出接口", notes = "将全量数据导出，并生成一个excel文件")
        #end
    @GetMapping(value = "/export")
    fun exportExcel() {
        super.exportExcel(super.getExportName("${table.comment}"), super.voClass, null)
    }
    #end
}
#else
class ${table.controllerName} {

}
#end