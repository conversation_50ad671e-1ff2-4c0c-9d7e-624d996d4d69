######################### 数据源配置 dataSourceConfig start #########################
# 数据库连接的url
db.url=*****************************************************************************************************************************************************************************************************************
# 数据库连接用户名
db.username=frp
# 数据库连接密码
db.password=frp20241101!.
# 数据库名称
db.schema=frp_business_dev
######################### 数据源配置 dataSourceConfig end ##########################
#########################  全局配置 globalConfig start #########################
# 生成文件的输出目录
gc.output=D:\\JavaCodes\\generatecodes
# 开发人员
gc.author=felix
# 时间类型对应策略：ONLY_DATE，使用java.util.date包下的类型；SQL_PACK，使用java.sql包下的类型；TIME_PACK，使用java.time包下的类型，表示java8新时间类型
gc.dateType=TIME_PACK
# 注释日期格式化
gc.commentDate=yyyy-MM-dd
# 代码生成完毕后是否打开输出目录（默认 true）
gc.open=true
# 开启 Kotlin 模式（默认 false）
gc.kotlin=false
# 是否开启swagger模式（默认 false）
gc.swagger=false
# 是否开启springdoc模式（默认 true）
gc.springdoc=true
# 是否生成service接口（默认 true）
gc.serviceInterface=true
#########################  全局配置 globalConfig end #########################
#
#########################  数据库表策略配置 strategyConfig start #########################
# 表示生成实体时忽略的表前缀名，如：表名为"wms_item_brand"，配置为"wms_"，生成后的实体为"ItemBrand"。多个以","隔开
sc.tablePrefix=
# 表示生成实体时忽略的表后缀名
sc.tableSuffix=
# 表示生成实体时忽略的字段前缀名
sc.fieldPrefix=
# 表示生成实体时忽略的字段后缀名
sc.fieldSuffix=
# 需要生成的表名，多个以","隔开，允许正则表达式
sc.include=otb_workorder,otb_prep_workorder
## 需要排除的表名，允许正则表达式（与include二选一配置）
#sc.exclude=
# 是否启用大写命名（默认 false）
sc.capitalMode=false
# 是否跳过视图（默认 true）
sc.skipView=true
# 是否启用sql过滤，语法不能支持使用sql过滤表的话，可以考虑关闭此开关（默认 true）
sc.sqlFilter=true
# 是否启用schema处理逻辑，若存在schemaName设置拼接 . 组合表名（默认 false）
sc.schema=false
#####  entity策略配置 #########################
# 自定义继承的Entity类全称，带包名
# sc.entity.superClass=cn.need.framework.common.mybatis.model.SuperModel
sc.entity.superClass=cn.need.framework.common.mybatis.model.TenantModel
# 自定义基础的Entity类，公共字段
sc.entity.superColumns=id,create_by,create_time,update_by,update_time,remove_flag,tenant_id,version
# 自定义忽略字段
sc.entity.ignoreColumns=
# 数据库表映射到实体的命名策略，默认下划线转驼峰命名
sc.entity.naming=underline_to_camel
# 数据库表字段映射到实体的命名策，默认下划线转驼峰命名
sc.entity.columnNaming=underline_to_camel
# 乐观锁字段名称(数据库字段)
sc.entity.versionColumnName=
# 乐观锁属性名称(实体字段)
sc.entity.versionPropertyName=
# 逻辑删除字段名称(数据库字段)
sc.entity.logicDeleteColumnName=
# 逻辑删除属性名称(实体字段)
sc.entity.logicDeletePropertyName=
# 格式化文件名称
sc.entity.formatFileName=%s
# 指定生成的主键的ID类型
sc.entity.idType=
# 【实体】是否生成 serialVersionUID（默认 ture）
sc.entity.serialVersionUID=ture
# 开启 ActiveRecord 模式（默认 false）
sc.entity.activeRecord=false
# 【实体】是否生成字段常量（默认 false）
sc.entity.columnConstant=false
# 【实体】是否为链式模型（默认 false）
sc.entity.chain=false
# 【实体】是否为lombok模型（默认 true）
sc.entity.lombok=true
# Boolean类型字段是否移除is前缀（默认 false）
# 比如 : 数据库字段名称 : 'is_xxx',类型为 : tinyint. 在映射实体的时候则会去掉is,在实体类中映射最终结果为 xxx
sc.entity.booleanColumnRemoveIsPrefix=false
# 是否生成实体时，生成字段注解（默认 true）
sc.entity.tableFieldAnnotationEnable=true
# 是否覆盖已有文件（默认 true）
sc.entity.fileOverride=true
#####  mapper接口策略配置 #########################
# 自定义继承的Mapper类全称，带包名
sc.mapper.superClass=cn.need.framework.common.mybatis.base.SuperMapper
# 自定义Mapper标记注解，带包名
sc.mapper.annotationClass=org.apache.ibatis.annotations.Mapper
# 格式化Mapper文件名称
sc.mapper.formatFileName=%sMapper
# 是否覆盖已有文件（默认 true）
sc.mapper.fileOverride=true
#####  service策略配置 #########################
# 自定义继承的Service类全称，带包名
sc.service.superClass=cn.need.framework.common.mybatis.base.SuperService
# 格式化service接口文件名称
sc.service.formatFileName=%sService
# 是否覆盖已有文件（默认 true）
sc.service.fileOverride=true
# 自定义继承的ServiceImpl类全称，带包名
sc.service.impl.superClass=cn.need.framework.common.mybatis.base.SuperServiceImpl
# 格式化service实现类文件名称
sc.service.impl.formatFileName=%sServiceImpl
#####  controller策略配置 #########################
# 自定义继承的Controller类全称，带包名
sc.controller.superClass=cn.need.framework.common.support.base.AbstractRestController
# 格式化controller文件名称
sc.controller.formatFileName=%sController
# 开启生成@RestController控制器（默认 true）
sc.controller.restStyle=true
# 开启驼峰转连字符（默认 true）
sc.controller.hyphenStyle=true
# 是否覆盖已有文件（默认 true）
sc.controller.fileOverride=true
#####  vo策略配置 #########################
# 自定义继承的VO类全称，带包名
#sc.vo.superClass=cn.need.framework.common.support.api.SuperVO
# vo文件路径
sc.vo.filePath=
# vo文件名称
sc.vo.fileName=VO
# 是否覆盖已有文件（默认 true）
sc.vo.fileOverride=true
# 是否开启vo生成策略（默认 true）
sc.vo.enable=true
#####  page vo策略配置 #########################
# 自定义继承的pageVO类全称，带包名
sc.pageVo.superClass=cn.need.framework.common.support.api.SuperVO
# page vo文件路径
sc.pageVo.filePath=
# page vo文件名称
sc.pageVo.fileName=PageVO
# 是否覆盖已有文件（默认 true）
sc.pageVo.fileOverride=true
# 是否开启page vo生成策略（默认 true）
sc.pageVo.enable=true
#####  query策略配置 #########################
# 自定义继承的Query类全称，带包名
# sc.query.superClass=cn.need.framework.common.support.api.SuperQuery
sc.query.superClass=cn.need.framework.common.support.api.FrpSuperQuery
# query文件路径
sc.query.filePath=
# query文件名称
sc.query.fileName=Query
# 是否覆盖已有文件（默认 true）
sc.query.fileOverride=true
# 是否开启query生成策略（默认 true）
sc.query.enable=true
#####  createParam策略配置 #########################
# 自定义继承的CreateParam类全称，带包名
#sc.createParam.superClasslass=cn.need.framework.common.support.api.SuperCreateParam
# createParam文件路径
sc.createParam.filePath=
# createParam文件名称
sc.createParam.fileName=CreateParam
# 是否覆盖已有文件（默认 true）
sc.createParam.fileOverride=true
# 是否开启createParam生成策略（默认 true）
sc.createParam.enable=true
#####  updateParam策略配置 #########################
# 自定义继承的UpdateParam类全称，带包名
#sc.updateParam.superClasslass=cn.need.framework.common.support.api.SuperUpdateParam
# updateParam文件路径
sc.updateParam.filePath=
# updateParam文件名称
sc.updateParam.fileName=UpdateParam
# 是否覆盖已有文件（默认 true）
sc.updateParam.fileOverride=true
# 是否开启updateParam生成策略（默认 true）
sc.updateParam.enable=true
#####  dto策略配置 #########################
# 自定义继承的DTO类全称，带包名
sc.dto.superClass=cn.need.framework.common.support.api.SuperDTO
# dto文件路径
sc.dto.filePath=
# dto文件名称
sc.dto.fileName=DTO
# 是否覆盖已有文件（默认 true）
sc.dto.fileOverride=true
# 是否开启dto生成策略（默认 true）
sc.dto.enable=true
#####  converter策略配置 #########################
# 自定义继承的Converter类全称，带包名
sc.converter.superClass=cn.need.framework.common.support.convert.AbstractModelConverter
# converter文件路径
sc.converter.filePath=
# converter文件名称
sc.converter.fileName=Converter
# 是否覆盖已有文件（默认 true）
sc.converter.fileOverride=true
# 是否开启converter生成策略（默认 true）
sc.converter.enable=true
#####  mapper xml策略配置 #########################
# mapper xml文件路径
sc.xml.filePath=
# 格式化Xml文件名称
sc.xml.formatFileName=%sMapper
# 是否开启BaseResultMap（默认 false）
sc.xml.baseResultMap=true
# 是否开启baseColumnList（默认 false）
sc.xml.baseColumnList=true
#########################  数据库表策略配置 strategyConfig end #########################
#
#########################  包名配置 packageConfig start #########################
# 父包名。如果为空，下面子包名必须写全部， 否则就只需写子包名
pc.parent=cn.need.cloud
# 父包模块名
pc.moduleName=biz
# Entity包名
pc.entity=model.entity
# Service包名
pc.service=service
# Service Impl包名
pc.serviceImpl=service.impl
# Mapper包名
pc.mapper=mapper
# Mapper XML包名
pc.xml=mapper
# Controller包名
pc.controller=controller
# VO包名
pc.vo=model.vo
# DTO包名
pc.dto=client.dto
# Converter包名
pc.converter=converter
# 连接父子包名
pc.joinPackage=
#########################  包名配置 packageConfig end #########################
#
#########################  模版配置 templateConfig start #########################
# Java 实体类模板
tp.java.entity=template/entity.java.vm
# Java Controller 控制器模板
tp.java.controller=template/controller.java.vm
# Java Mapper 模板
tp.java.mapper=template/mapper.java.vm
# Java Service 类模板
tp.java.service=template/service.java.vm
# Java Service Impl 实现类模板
tp.java.serviceImpl=template/serviceImpl.java.vm
# Java VO 模板
tp.java.vo=template/vo.java.vm
# Java DTO 模板
tp.java.dto=template/dto.java.vm
# Java Converter 模板
tp.java.converter=template/converter.java.vm
# kotlin 实体类模板
tp.kotlin.entity=template/entity.kt.vm
# kotlin controller 控制器模板
tp.kotlin.controller=template/controller.kt.vm
# kotlin Mapper 模板
tp.kotlin.mapper=template/mapper.kt.vm
# kotlin Service 类模板
tp.kotlin.service=template/service.kt.vm
# kotlin Service Impl 实现类模板
tp.kotlin.serviceImpl=template/serviceImpl.kt.vm
# kotlin VO 模板
tp.kotlin.vo=template/vo.query.vm
# kotlin DTO 模板
tp.kotlin.dto=template/dto.kt.vm
# kotlin Converter 模板
tp.kotlin.converter=template/converter.kt.vm
# Mapper xml 模板
tp.xml=template/mapper.xml.vm
# 禁用模板，取值范围：ENTITY,SERVICE,SERVICE_IMPL,CONTROLLER,MAPPER,XML
tp.disable=
#########################  模版配置 templateConfig end #########################
#
######################### 自定义参数配置 start #########################
# java版本
cfg.javaVersion=17
# 项目前缀名，用于输出目录生成项目名用
cfg.projectPrefix=need-cloud
# server项目后缀
cfg.serverSuffix=server
# client项目后缀
cfg.clientSuffix=client
# 这里自定了Controller类上的请求路径前缀，路径为前缀+模块名称+请求路径，如：@RequestMapping("/api/item/unit")
cfg.requestPrefix=api
# 占位文件名字
cfg.placeholderFileName=package-info.java
# 是否生成占位文件（默认 true）
cfg.enablePlaceholder=true
# 标记controller是否生成新增数据的方法（默认 true）
cfg.controllerInsert=true
# 标记controller是否生成修改数据的方法（默认 true）
cfg.controllerUpdate=true
# 标记controller是否生成删除数据的方法（默认 true）
cfg.controllerRemove=true
# 标记controller是否生成设置数据有效性的方法（默认 false）
cfg.controllerActive=false
# 标记controller是否生成获取数据详情的方法（默认 true）
cfg.controllerDetail=true
# 标记controller是否生成获取数据列表的方法（默认 true）
cfg.controllerList=true
# 标记controller是否生成通用导入方法（默认 false）
cfg.controllerImport=false
# 标记controller是否生成通用导出方法（默认 false）
cfg.controllerExport=false
######################### 自定义参数配置 end #########################
