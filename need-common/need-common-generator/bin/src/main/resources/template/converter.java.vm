package ${converterPackage};

#if(${superConverterClassPackage})
import ${dtoPackage}.${dtoName};
import ${package.Entity}.${entity};
import ${voPackage}.${voName};
import ${superConverterClassPackage};
#end

/**
 * <p>
 * ${table.comment} 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${superConverterClass})
public class ${converterName} extends ${superConverterClass}<${entity}, ${voName}, ${dtoName}> {
#else
public class ${converterName} {
#end

}
