<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package.Mapper}.${table.mapperName}">
#set($alias = "")
#set($tableNameLower = $table.name.toLowerCase())
#set($parts = $tableNameLower.split("_"))
#foreach($part in $parts)
    #if($part.length() > 0)
        #set($alias = $alias + $part.substring(0,1))
    #end
#end
#set($tableAlias = $alias)
#if(${enableCache})
    <!-- 开启二级缓存 -->
    <cache type="${cacheClassName}"/>

#end
#if(${baseResultMap})
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="${package.Entity}.${entity}">
#foreach($field in ${table.fields})
#if(${field.keyFlag})##生成主键排在第一位
        <id column="${field.name}" property="${field.propertyName}" />
#end
#end
#foreach($field in ${table.commonFields})##生成公共字段
        <result column="${field.name}" property="${field.propertyName}" />
#end
#foreach($field in ${table.fields})
#if(!${field.keyFlag})##生成普通字段
        <result column="${field.name}" property="${field.propertyName}" />
#end
#end
    </resultMap>

#end
###if(${baseColumnList})
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    #foreach($field in ${table.commonFields})
        ${dbTableAlias}.${field.columnName},
    #end
    #foreach($field in ${table.fields})
        ${dbTableAlias}.${field.annotationColumnName}#if($foreach.index < ${table.fields.size()} - 1),#end
    #end
    </sql>
###end
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="${voPackage}.${entity}PageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="${tableAlias}"/>
            </include>
        FROM
        ${schemaName}${table.name} ${tableAlias}
        WHERE
            ${tableAlias}.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="${tableAlias}"/>
                    <property name="qoTableAlias" value="qo${tableAlias}"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="${tableAlias}"/>
                <property name="qoTableAlias" value="qo${tableAlias}"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qo${tableAlias}.timeZone != null and qo${tableAlias}.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo${tableAlias}.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            ${schemaName}${table.name} ${tableAlias}
        WHERE
            ${tableAlias}.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="${tableAlias}"/>
                    <property name="qoTableAlias" value="qo${tableAlias}"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qo${tableAlias}.timeZone != null and qo${tableAlias}.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo${tableAlias}.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        #foreach($field in ${table.fields})
    #if(${field.propertyType.equals("String")})
        <if test="${qoTableAlias}.${field.propertyName} != null and ${qoTableAlias}.${field.propertyName} != ''">
            AND ${dbTableAlias}.${field.annotationColumnName} = #{${qoTableAlias}.${field.propertyName}}
        </if>
    #elseif(${field.propertyType.equals("LocalDateTime")})
        <if test="${qoTableAlias}.${field.propertyName}Start != null">
            AND ${dbTableAlias}.${field.annotationColumnName}  &gt;= #{${qoTableAlias}.${field.propertyName}Start}
        </if>
        <if test="${qoTableAlias}.${field.propertyName}End != null">
            AND ${dbTableAlias}.${field.annotationColumnName}  &lt; #{${qoTableAlias}.${field.propertyName}End}
        </if>
    #else
        <if test="${qoTableAlias}.${field.propertyName} != null">
            AND ${dbTableAlias}.${field.annotationColumnName} = #{${qoTableAlias}.${field.propertyName}}
        </if>
    #end
            #if(${field.propertyName.endsWith("RefNum")}
            || ${field.propertyName.equals("refNum")}
            || ${field.propertyName.endsWith("Status")}
            || ${field.propertyName.equals("status")}
            || ${field.propertyName.endsWith("Type")}
            || ${field.propertyName.equals("type")}
            || ${field.propertyName.endsWith("Upc")}
            || ${field.propertyName.equals("upc")}
            || ${field.propertyName.endsWith("SupplierSku")}
            || ${field.propertyName.equals("supplierSku")}
            || ${field.propertyName.endsWith("ProductBarcode")}
            || ${field.propertyName.equals("productBarcode")}
            || ${field.propertyName.endsWith("Channel")}
            || ${field.propertyName.equals("channel")}
            || ${field.propertyName.endsWith("TransactionPartnerId")}
            || ${field.propertyName.equals("transactionPartnerId")}
            || ${field.propertyName.endsWith("TransactionPartnerId")}
            || ${field.propertyName.equals("transactionPartnerId")}
            || ${field.propertyName.endsWith("Station")}
            || ${field.propertyName.equals("station")}
            || ${field.propertyName.endsWith("BolNum")}
            || ${field.propertyName.equals("bolNum")}
            || ${field.propertyName.endsWith("Ssccnum")}
            || ${field.propertyName.equals("ssccnum")}
            || ${field.propertyName.endsWith("TrackingNum")}
            || ${field.propertyName.equals("trackingNum")}
            || ${field.propertyName.endsWith("ShipCarrier")}
            || ${field.propertyName.equals("shipCarrier")}
            || ${field.propertyName.endsWith("ShipMethod")}
            || ${field.propertyName.equals("shipMethod")}
            || ${field.propertyName.endsWith("Id")}
            || ${field.propertyName.endsWith("Type")}
            || ${field.propertyName.endsWith("Status")}
            )
    <if test="${qoTableAlias}.${field.propertyName}List != null and ${qoTableAlias}.${field.propertyName}List.size > 0 ">
        AND ${dbTableAlias}.${field.annotationColumnName} in
        <foreach collection="${qoTableAlias}.${field.propertyName}List" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
            #end
        #end
    </sql>
##    <!-- 通用查询条件 -->
##    <sql id="Base_Where_List">
##    <if test="${qoTableAlias} != null">
##        <if test="${qoTableAlias}.createTimeStart != null">
##            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
##        </if>
##        <if test="${qoTableAlias}.createTimeEnd != null">
##            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
##        </if>
##        <if test="${qoTableAlias}.updateTimeStart != null">
##            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
##        </if>
##        <if test="${qoTableAlias}.updateTimeEnd != null">
##            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
##        </if>
##    #foreach($field in ${table.commonFields})
##        <include refid="mapper.valueConditions">
##            <property name="dbColumnName" value="${dbTableAlias}.${field.annotationColumnName}"/>
##            <property name="qoColumnName" value="${qoTableAlias}.${field.propertyName}"/>
##        </include>
##    #end
##
##    #foreach($field in ${table.fields})
####        #if(${field.propertyType.equals("String")})
######        <if test="${qoTableAlias}.${field.propertyName} != null and ${qoTableAlias}.${field.propertyName} != ''">
######                AND ${dbTableAlias}.${field.annotationColumnName} = #{${qoTableAlias}.${field.propertyName}}
######        </if>
####        #elseif(${field.propertyType.equals("LocalDateTime")})
####            <if test="${qoTableAlias}.${field.propertyName}Start != null">
####                AND ${dbTableAlias}.${field.annotationColumnName} &gt;= #{${qoTableAlias}.${field.propertyName}Start}
####        </if>
####            <if test="${qoTableAlias}.${field.propertyName}End != null">
####                AND ${dbTableAlias}.${field.annotationColumnName} &lt; #{${qoTableAlias}.${field.propertyName}End}
####        </if>
####        #else
######         <if test="${qoTableAlias}.${field.propertyName} != null">
######                AND ${dbTableAlias}.${field.annotationColumnName} = #{${qoTableAlias}.${field.propertyName}}
######        </if>
####        #end
##        <include refid="mapper.valueConditions">
##            <property name="dbColumnName" value="${dbTableAlias}.${field.annotationColumnName}"/>
##            <property name="qoColumnName" value="${qoTableAlias}.${field.propertyName}"/>
##        </include>
##
##    #end
##    </if>
##    </sql>

    <select id="listByQueryPro" resultType="${voPackage}.${entity}PageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="${tableAlias}"/>
           </include>
        FROM
            ${schemaName}${table.name} ${tableAlias}
        WHERE
            ${tableAlias}.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="${tableAlias}" />
                <property name="qoTableAlias" value="qo${tableAlias}" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="${tableAlias}" />
                <property name="qoTableAlias" value="qo${tableAlias}" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="${tableAlias}"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM ${schemaName}${table.name} ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="${package.Mapper}.${table.mapperName}.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>