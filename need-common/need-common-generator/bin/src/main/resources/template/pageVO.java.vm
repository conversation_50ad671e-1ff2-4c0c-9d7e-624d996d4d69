package ${pageVoPackage};

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;

import java.util.List;
#if(${superPageVoClassPackage})
import ${superPageVoClassPackage};
#end
#if(${springdoc})
import io.swagger.v3.oas.annotations.media.Schema;
#elseif(${swagger})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
    #if(${superPageVoClass})
    import lombok.EqualsAndHashCode;
    #end
#end

#if(${entitySerialVersionUID})
    #if($javaVersion > 14)
    import java.io.Serial;
    #end
    #if(!${superPageVoClass})
    import java.io.Serializable;
    #end
#end

/**
 * $!{table.comment} 分页列表VO对象
 *
 * <AUTHOR>
 * @since ${date}
 */
    #if(${entityLombokModel})
    @Data
        #if(${superPageVoClass})
        @EqualsAndHashCode(callSuper = true)
        #end
    #end
    #if(${springdoc})
    @Schema(description = "$!{table.comment} 分页列表VO对象")
    #elseif(${swagger})
    @ApiModel(value = "${pageVoName}", description = "${table.comment}  分页列表VO对象")
    #end
    #if(${superPageVoClass})
    public class ${pageVoName} extends ${superPageVoClass} {
    #else
            public class ${pageVoName} implements Serializable {
    #end

    #if(${entitySerialVersionUID})
        #if($javaVersion > 14)
        @Serial
        #end
    private static final long serialVersionUID = 1L;
    #end
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    ## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if("$!field.comment" != "")
            #set($propertyVal=${field.comment})
        #else
            #set($propertyVal=${field.propertyName})
        #end
        /**
         * ${propertyVal}
         */
        #if(${springdoc})
        @Schema(description = "${propertyVal}")
        #elseif(${swagger})
        @ApiModelProperty(value = "${propertyVal}")
        #end
    private ${field.propertyType} ${field.propertyName};

    #end
    ## ----------  END 字段循环遍历  ----------
    #if(!${entityLombokModel})

        ## ----------  字段循环遍历  ----------
        #foreach($field in ${table.fields})
            #if(${field.propertyType.equals("boolean")})
                #set($getprefix="is")
            #else
                #set($getprefix="get")
            #end
            public ${field.propertyType} ${getprefix}${field.capitalName}() {
            return this.${field.propertyName};
        }

            public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
            this.${field.propertyName} = ${field.propertyName};
        }
        #end

        @Override
        public String toString() {
        return "${pageVoName} {" +
                "id=" + id +
            #foreach($field in ${table.fields})
                    ", ${field.propertyName}=" + ${field.propertyName} +
            #end
                "} ";
    }
    #end

}