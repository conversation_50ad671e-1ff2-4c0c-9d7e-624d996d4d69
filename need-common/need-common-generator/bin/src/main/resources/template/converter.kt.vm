package ${converterPackage};

#if(${superConverterClassPackage})
import ${package.Entity}.${entity};
import ${dtoPackage}.${dtoName};
import ${voPackage}.${voName};
import ${superConverterClassPackage};
#end

/**
 * <p>
 * ${table.comment} 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${superConverterClass})
class ${converterName} : ${superConverterClass}<${entity}, ${voName}, ${dtoName>() {

}
class ${converterName} {

}
#end