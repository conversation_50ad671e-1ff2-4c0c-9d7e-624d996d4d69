package ${package.Service};

import java.util.List;
import ${package.Entity}.${entity};
import ${pageVoPackage}.${pageVoName};
import ${voPackage}.${voName};
import ${queryPackage}.${queryName};
import ${createParamPackage}.${createParamName};
import ${updateParamPackage}.${updateParamName};
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
#if(${superServiceClassPackage})
import ${package.Entity}.${entity};
import ${superServiceClassPackage};
#end

/**
 * <p>
 * $!{table.comment} service 接口
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${superServiceClass})
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {
#else
public interface ${table.serviceName} {
#end

    /**
     * 根据参数新增$!{table.comment}
     *
     * @param createParam 请求创建参数，包含需要插入的$!{table.comment}的相关信息
     * @return $!{table.comment}ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(${entity}CreateParam createParam);


    /**
     * 根据参数更新$!{table.comment}
     *
     * @param updateParam 请求创建参数，包含需要更新的$!{table.comment}的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(${entity}UpdateParam updateParam);

    /**
     * 根据查询条件获取$!{table.comment}列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个$!{table.comment}对象的列表(分页)
     */
    List<${entity}PageVO> listByQuery(${entity}Query query);

    /**
     * 根据查询条件获取$!{table.comment}列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个$!{table.comment}对象的列表(分页)
     */
    PageData<${entity}PageVO> pageByQuery(PageSearch<${entity}Query> search);

    /**
     * 根据ID获取$!{table.comment}
     *
     * @param id $!{table.comment}ID
     * @return 返回$!{table.comment}VO对象
     */
    ${entity}VO detailById(Long id);

#foreach($field in ${table.fields})
    #if(${field.propertyName.equals("refNum")})
        /**
         * 根据$!{table.comment}唯一编码获取$!{table.comment}
         *
         * @param refNum $!{table.comment}唯一编码
         * @return 返回$!{table.comment}VO对象
         */
            ${entity}VO detailByRefNum(String refNum);
    #end
#end


    #if(${parentEntityName})
    /**
     * 根据$!{parentComment}id获取$!{table.comment}集合
     *
     * @param ${lowerParentEntityName}Id $!{parentComment}id
     * @return $!{table.comment}集合
     */
    List<${entity}VO> listBy${parentEntityName}Id(Long ${lowerParentEntityName}Id);
    #end

}