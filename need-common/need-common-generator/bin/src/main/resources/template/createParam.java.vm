package ${createParamPackage};

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
#if(${superCreateParamClassPackage})
import ${superCreateParamClassPackage};
#end
#if(${springdoc})
import io.swagger.v3.oas.annotations.media.Schema;
#elseif(${swagger})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
    #if(${superCreateParamClass})
    import lombok.EqualsAndHashCode;
    #end
#end

#if(${entitySerialVersionUID})
    #if($javaVersion > 14)
    import java.io.Serial;
    #end
    #if(!${superCreateParamClass})
    import java.io.Serializable;
    #end
#end

/**
 * $!{table.comment} CreateParam对象
 *
 * <AUTHOR>
 * @since ${date}
 */
    #if(${entityLombokModel})
    @Data
        #if(${superCreateParamClass})
            ##@EqualsAndHashCode(callSuper = true)
        #end
    #end
    #if(${springdoc})
    @Schema(description = "$!{table.comment} CreateParam对象")
    #elseif(${swagger})
    @ApiModel(value = "${createParamName}", description = "${table.comment} CreateParam对象")
    #end
    #if(${superCreateParamClass})
    public class ${createParamName} extends ${superCreateParamClass}{
    #else
            public class ${createParamName} implements Serializable {
    #end

    #if(${entitySerialVersionUID})
        #if($javaVersion > 14)
        @Serial
        #end
    private static final long serialVersionUID = 1L;
    #end
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    ## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if("$!field.comment" != "")
            #set($propertyVal=${field.comment})
        #else
            #set($propertyVal=${field.propertyName})
        #end
        /**
         * ${propertyVal}
         */
        #if(${springdoc})
        @Schema(description = "${propertyVal}")
        #elseif(${swagger})
        @ApiModelProperty(value = "${propertyVal}")
        #end
        #if(!${field.metaInfo.nullable})
            #if(${field.propertyType.equals("Boolean")}||
                ${field.propertyType.equals("Integer")}||
                ${field.propertyType.equals("Long")}||
                ${field.propertyType.equals("Double")}||
                ${field.propertyType.equals("Float")} ||
                ${field.propertyType.equals("LocalDateTime")} )
            @NotNull(message = "${field.propertyName} cannot be null")
            #else
            @NotEmpty(message = "${field.propertyName} cannot be empty")
            #end
        #end
        #if(${field.propertyType.equals("String")})
        @Size(max = ${field.metaInfo.length}, message = "${field.propertyName} cannot exceed ${field.metaInfo.length} characters")
        #end
    private ${field.propertyType} ${field.propertyName};
    #end
    ## ----------  END 字段循环遍历  ----------
    #if(!${entityLombokModel})

        ## ----------  字段循环遍历  ----------
        #foreach($field in ${table.fields})
            #if(${field.propertyType.equals("boolean")})
                #set($getprefix="is")
            #else
                #set($getprefix="get")
            #end
            public ${field.propertyType} ${getprefix}${field.capitalName}() {
            return this.${field.propertyName};
        }

            public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
            this.${field.propertyName} = ${field.propertyName};
        }
        #end

        @Override
        public String toString() {
        return "${createParamName} {" +
                "id=" + id +
            #foreach($field in ${table.fields})
                    ", ${field.propertyName}=" + ${field.propertyName} +
            #end
                "} ";
    }
    #end

}