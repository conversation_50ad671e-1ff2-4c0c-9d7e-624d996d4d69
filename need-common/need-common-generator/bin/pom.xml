<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.need.framework</groupId>
        <artifactId>need-parent</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>
    <groupId>cn.need.framework.common</groupId>
    <artifactId>need-common-generator</artifactId>
    <packaging>jar</packaging>
    <name>need-common-generator</name>
    <description>Uneed common util for generator</description>

    <!-- 版本参数 -->
    <properties>
        <mybatis-plus-generator.version>3.4.1</mybatis-plus-generator.version>
    </properties>

    <!-- 依赖配置 -->
    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
<!--            <version>${mybatis-plus-generator.version}</version>-->
        </dependency>

        <!-- spring web-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- mysql driver -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- log -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>

        <!-- 模板引擎 -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibeetl</groupId>
            <artifactId>beetl</artifactId>
        </dependency>

        <!-- coracle util -->
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-mybatis</artifactId>
        </dependency>
    </dependencies>

    <build>

    </build>

    <developers>
        <developer>
            <name>diablo</name>
            <email><EMAIL></email>
        </developer>
    </developers>
</project>
