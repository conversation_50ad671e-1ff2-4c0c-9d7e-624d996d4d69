######################### 数据源配置 dataSourceConfig start #########################
# 数据库驱动名称
db.driverName=com.mysql.cj.jdbc.Driver
# 驱动连接的url
db.url=*******************************************************************************************************
# 数据库连接用户名
db.username=root
# 数据库连接密码
db.password=ZSWY%H#K&T9Mj
######################### 数据源配置 dataSourceConfig end #########################
#########################  全局配置 globalConfig start #########################
# 生成文件的输出目录
gc.output=/Users/<USER>/code_template/database
# 代码生成完毕后是否打开输出目录（默认 true）
gc.open=true
# 生成文档类型 (WORD文件:doc,HTML文件:html,Markdown文件:md)
gc.fileType=
# 生成文档标题
gc.title=招商erp采购中心数据库设计文档
# 生成文档名称
gc.fileName=招商erp采购中心数据库设计文档
# 文档版本
gc.version=2022.0.1
# 文档描述
gc.description=招商erp采购中心相关表结构设计初版
# 需要忽略的表
gc.ignore=
# 需要忽略的表前缀
gc.ignorePrefix=
# 需要忽略的表后缀
gc.ignoreSuffix=
# 需要生成的表
gc.designated=
# 需要生成的表前缀
gc.designatedPrefix=
# 需要生成的表后缀
gc.designatedSuffix=
#########################  全局配置 globalConfig end #########################
