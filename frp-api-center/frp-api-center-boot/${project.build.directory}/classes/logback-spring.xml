<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">

    <!-- 读取spring.application.name中的属性来生成日志文件名 -->
    <!--    <springProperty scop="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>-->
    <!--    <property name="log.path" value="logs/${spring.application.name}"/>-->

    <!--日志根path ,因为目前dev,test环境applicationName是动态加了group后缀生成的，为了日志目录不随意变化，则固定一个没有后缀目录-->
    <property name="appName" value="frp-api-center"/>
    <property name="log.path" value="/data/logs/${appName}"/>

    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS X}){faint} ${appName} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([${PID:- }]){magenta} %clr([%thread]){faint} %clr([%logger{50}]){cyan} %clr([%file:%line] : ){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS X} ${appName} %-5level [${PID:- }] [%thread] [%logger{50}] [%file:%line] : %msg%n"/>


    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- Log file debug output -->
    <appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/application.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- Log file error output -->
    <appender name="errorLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--过滤只采集ERROR级别日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 开发，测试，uat，生产环境激活该日志配置-->
    <springProfile name="dev,local">
        <logger name="com.ipower" level="DEBUG" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </logger>
        <logger name="cn.need" level="DEBUG" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </logger>
        <!--nacos 心跳 INFO 屏蔽-->
        <logger name="com.alibaba.nacos" level="OFF">
            <appender-ref ref="console"/>
        </logger>
        <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </root>
    </springProfile>

    <springProfile name="test,uat,prod">
        <logger name="com.ipower" level="INFO" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </logger>
        <logger name="cn.need" level="INFO" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </logger>
        <!--nacos 心跳 INFO 屏蔽-->
        <logger name="com.alibaba.nacos" level="OFF">
            <appender-ref ref="console"/>
        </logger>
        <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </root>
    </springProfile>


  <!--  <springProfile name="prod">
        <logger name="com.ipower" level="WARN" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </logger>
        <logger name="cn.need" level="WARN" additivity="false">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </logger>
        &lt;!&ndash;nacos 心跳 INFO 屏蔽&ndash;&gt;
        <logger name="com.alibaba.nacos" level="OFF">
            <appender-ref ref="console"/>
        </logger>
        &lt;!&ndash; Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 &ndash;&gt;
        <root level="ERROR">
            <appender-ref ref="console"/>
            <appender-ref ref="logFile"/>
            <appender-ref ref="errorLogFile"/>
        </root>
    </springProfile>-->

</configuration>
