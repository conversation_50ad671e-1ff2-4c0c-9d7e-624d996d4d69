spring:
  # 服务名称
  application:
    name: frp-api-center-felix
  # 配置spring cloud的配置中心，采用alibaba的nacos做为配置中心
  cloud:
    nacos:
      config:
        server-addr: ***************:30204
        file-extension: yaml
        # nacos命名空间
        namespace: dev
        username: ipower
        password: ipower20250513!.
        # 分组
        group: felix
        # nacos共享配置
        shared-configs:
          - data-id: frp-database.yaml
            group: DEFAULT_GROUP
          - data-id: frp-redis.yaml
            group: DEFAULT_GROUP
          - data-id: frp-feign.yaml
            group: DEFAULT_GROUP
          - data-id: frp-security.yaml
            group: DEFAULT_GROUP
          - data-id: frp-xxl-job.yaml
            group: DEFAULT_GROUP
          - data-id: frp-tenant.yaml
            group: DEFAULT_GROUP
      discovery:
        server-addr: ***************:30204
        # 命名空间
        namespace: dev
        username: ipower
        password: ipower20250513!.
