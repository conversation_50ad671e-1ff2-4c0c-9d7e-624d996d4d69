package cn.need.cloud.apicenter.frp.service.transfer;

import cn.need.cloud.biz.client.dto.req.transfer.TransferOwnerShipRequestReqDTO;
import cn.need.framework.common.support.api.Result;

/**
 * 货权转移请求管理 服务类
 * <p>
 * 该接口提供货权转移相关的远程调用功能，包括货权转移的创建和审核等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
public interface TransferOwnerShipRequestService {

    /**
     * 创建并审核货权转移请求
     * <p>
     * 创建并审核货权转移请求
     * </p>
     *
     * @param reqDTO 货权转移请求数据传输对象，包含货权转移请求的详细信息
     * @return Result<Boolean> 包含创建并审核货权转移请求的结果
     */
    Result<Boolean> createWithAudit(TransferOwnerShipRequestReqDTO reqDTO);
}
