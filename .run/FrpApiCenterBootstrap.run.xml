<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="FrpApiCenterBootstrap" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <module name="frp-api-center-boot" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="cn.need.cloud.apicenter.frp.FrpApiCenterBootstrap" />
    <option name="UPDATE_ACTION_UPDATE_POLICY" value="UpdateClassesAndTriggerFile" />
    <option name="VM_PARAMETERS" value=" -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxMetaspaceSize=256m" />
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/pom.xml" goal="clean:clean" />
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>