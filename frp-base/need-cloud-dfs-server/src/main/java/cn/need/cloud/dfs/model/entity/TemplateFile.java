package cn.need.cloud.dfs.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 模版文件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dfs_template_file")
public class TemplateFile extends SuperModel {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件模板编号
     */
    @TableField("template_code")
    private String templateCode;

    /**
     * 导出文件名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 导入模板路径
     */
    @TableField("path")
    private String path;

    /**
     * 导入API
     */
    @TableField("api")
    private String api;

    /**
     * 状态 1:有效 0:无效
     */
    @TableField("state")
    private Integer state;

    /**
     * 模板下载地址
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 附件id
     */
    @TableField("attach_id")
    private Long attachId;


}
