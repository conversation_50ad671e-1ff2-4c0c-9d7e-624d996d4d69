<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.dfs.mapper.AttachMapper">


    <select id="pageByCondition" resultType="cn.need.cloud.dfs.model.vo.resp.AttachRespVO">
        SELECT
        a.id,
        a.link,
        a.domain,
        a.name,
        a.original_name,
        a.extension,
        a.attach_size,
        a.file_name,
        DATE_FORMAT( a.create_time, '%Y-%m-%d %H:%i:%s') upload_time
        FROM
        dfs_attach a where a.remove_flag = 0
        <if test="condition.id != null">
            and a.id = #{condition.id}
        </if>
        <if test="condition.link != null and condition.link != ''">
            and a.link like concat('%', #{condition.link},'%')
        </if>
        <if test="condition.domain != null and condition.domain != ''">
            and a.domain like concat('%', #{condition.domain},'%')
        </if>
        <if test="condition.originalName != null and condition.originalName != ''">
            and a.original_name like concat('%', #{condition.originalName},'%')
        </if>
        <if test="condition.fileName != null and condition.fileName != ''">
            and a.file_name like concat('%', #{condition.fileName},'%')
        </if>
        order a.id desc
    </select>
</mapper>
