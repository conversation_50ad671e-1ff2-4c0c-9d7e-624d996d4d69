package cn.need.cloud.dict.client.api.path;

/**
 * 自定义序列数字生成client路径类
 *
 * <AUTHOR>
 */
public class NumberGeneratePath {

    /**
     * 前缀
     */
    public static final String PREFIX = "/client/dict/number-generate";

    /**
     * 初始化配置
     */
    public static final String INIT = "/config/init";

    /**
     * 生成编码
     */
    public static final String GENERATE_NUMBER = "/code";
    public static final String GENERATE_NUMBER_ZONE = "/zone-id";
    public static final String GENERATE_NUMBER_WAREHOUSE_CODE = "/warehouse-code";

    public static final String GENERATE_NUMBER_PRODUCT_CODE = "/product-code";
    public static final String GENERATE_NUMBER_WAREHOUSE_CODE_DELIMITER = "/warehouse-code-delimiter";
}
