<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.log.mapper.RecordSearchMapper">

    <delete id="deleteByRecordIds" parameterType="java.util.List">
        DELETE
        FROM
        log_record_search
        WHERE
        record_id IN
        <foreach collection="records" item="record" open="(" separator="," close=")">
            #{record}
        </foreach>
    </delete>

</mapper>