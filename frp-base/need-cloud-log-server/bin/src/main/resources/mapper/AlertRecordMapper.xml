<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.log.mapper.AlertRecordMapper">
    <select id="pageList" resultType="cn.need.cloud.log.model.vo.AlertRecordVO">
        SELECT
        l.id,
        l.source,
        l.keyword,
        l.module,
        l.function_desc,
        l.alert_info,
        l.request_url,
        l.create_time
        <include refid="listCondition"/>
        GROUP BY l.id desc
        <if test="offset !=null">
            <!-- 分页-->
            LIMIT #{offset}, #{size}
        </if>
    </select>

    <select id="pageCount" resultType="java.lang.Integer">
        SELECT count(l.id)
        <include refid="listCondition"/>
    </select>


    <sql id="listCondition">
        FROM
        log_alert_record l
        WHERE
        l.remove_flag = 0
        <if test="condition.source!=null and condition.source.toString() != '' ">
            AND l.source = #{condition.source}
        </if>
        <if test="condition.keyword!=null and condition.keyword.toString() != '' ">
            AND l.keyword like CONCAT('%',#{condition.keyword},'%')
        </if>
        <if test="condition.module!=null and condition.module.toString() != '' ">
            AND l.module like CONCAT('%',#{condition.module},'%')
        </if>
        <if test="condition.startTime!=null and condition.startTime.toString() != '' ">
            AND l.create_time <![CDATA[>=]]>  #{condition.startTime}
        </if>
        <if test="condition.endTime!=null and condition.endTime.toString() != '' ">
            AND l.create_time <![CDATA[<=]]>  #{condition.endTime}
        </if>
    </sql>


    <update id="truncateTable">
        truncate log_alert_record
    </update>

</mapper>
