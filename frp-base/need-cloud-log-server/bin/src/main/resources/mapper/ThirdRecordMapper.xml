<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.log.mapper.ThirdRecordMapper">

    <delete id="deleteByRecordIds" parameterType="java.util.List">
        DELETE
        FROM
        log_third_record
        WHERE
        record_id IN
        <foreach collection="records" item="record" open="(" separator="," close=")">
            #{record}
        </foreach>
    </delete>

    <select id="listByThirdRecordCondition" resultType="cn.need.cloud.log.model.entity.ThirdRecord"
            parameterType="cn.need.cloud.log.model.vo.req.ThirdRecordReqVO">
        SELECT
        *
        FROM
        log_third_record a
        WHERE
        a.remove_flag = 0
        <!--业务模块-->
        <if test="condition.operationModule != null and condition.operationModule != ''">
            AND a.operation_module = #{condition.operationModule}
        </if>
        <!--接口路径/描述-->
        <if test="condition.requestPath != null and condition.requestPath != ''">
            AND ( a.request_path LIKE CONCAT('%',#{condition.requestPath},'%') OR a.request_description LIKE
            CONCAT('%',#{condition.requestPath},'%'))
        </if>
        <!--业务系统-->
        <if test="condition.requestUser != null and condition.requestUser != ''">
            AND a.request_user LIKE CONCAT('%',#{condition.requestUser},'%')
        </if>
        <!--响应状态-->
        <if test="condition.responseStatus != null and condition.responseStatus != ''">
            AND a.response_status = #{condition.responseStatus}
        </if>
        <!--响应编码-->
        <if test="condition.responseCode != null and condition.responseCode != ''">
            AND a.response_code = #{condition.responseCode}
        </if>
        <!--请求开始时间-->
        <if test="condition.startTime != null and condition.startTime != ''">
            AND a.request_time <![CDATA[ >= ]]> #{condition.startTime}
        </if>
        <!--请求结束时间-->
        <if test="condition.endTime != null and condition.endTime != ''">
            AND a.request_time <![CDATA[ <= ]]>#{condition.endTime}
        </if>
        <!--请求参数-->
        <if test="condition.requestBody != null and condition.requestBody != ''">
            AND a.record_id IN ( SELECT b.record_id FROM log_request_data b WHERE b.remove_flag = 0 AND b.request_body
            LIKE
            CONCAT('%',#{condition.requestBody},'%') )
        </if>
        <!--关键字-->
        <if test="condition.keyword != null and condition.keyword != ''">
            AND a.record_id IN ( SELECT c.record_id FROM log_record_search c WHERE c.remove_flag = 0 AND c.keyword =
            #{condition.keyword} )
        </if>
        <!--排序-->
        ORDER BY a.id DESC
        <!--分页-->
        LIMIT #{offset},#{size}
    </select>

    <select id="countThirdRecordByCondition" resultType="java.lang.Long"
            parameterType="cn.need.cloud.log.model.vo.req.ThirdRecordReqVO">
        SELECT
        count(*)
        FROM
        log_third_record a
        WHERE
        a.remove_flag = 0
        <!--业务模块-->
        <if test="condition.operationModule != null and condition.operationModule != ''">
            AND a.operation_module = #{condition.operationModule}
        </if>
        <!--接口路径/描述-->
        <if test="condition.requestPath != null and condition.requestPath != ''">
            AND ( a.request_path LIKE CONCAT('%',#{condition.requestPath},'%') OR a.request_description LIKE
            CONCAT('%',#{condition.requestPath},'%'))
        </if>
        <!--业务系统-->
        <if test="condition.requestUser != null and condition.requestUser != ''">
            AND a.request_user LIKE CONCAT('%',#{condition.requestUser},'%')
        </if>
        <!--响应状态-->
        <if test="condition.responseStatus != null and condition.responseStatus != ''">
            AND a.response_status = #{condition.responseStatus}
        </if>
        <!--响应编码-->
        <if test="condition.responseCode != null and condition.responseCode != ''">
            AND a.response_code = #{condition.responseCode}
        </if>
        <!--请求开始时间-->
        <if test="condition.startTime != null and condition.startTime != ''">
            AND a.request_time <![CDATA[ >= ]]> #{condition.startTime}
        </if>
        <!--请求结束时间-->
        <if test="condition.endTime != null and condition.endTime != ''">
            AND a.request_time <![CDATA[ <= ]]>#{condition.endTime}
        </if>
        <!--请求参数-->
        <if test="condition.requestBody != null and condition.requestBody != ''">
            AND a.record_id IN ( SELECT b.record_id FROM log_request_data b WHERE b.remove_flag = 0 AND b.request_body
            LIKE
            CONCAT('%',#{condition.requestBody},'%') )
        </if>
        <!--关键字-->
        <if test="condition.keyword != null and condition.keyword != ''">
            AND a.record_id IN ( SELECT c.record_id FROM log_record_search c WHERE c.remove_flag = 0 AND c.keyword =
            #{condition.keyword} )
        </if>
    </select>
</mapper>