package cn.need.cloud.upms.service.user;

import cn.need.cloud.upms.client.constant.enums.AccountTypeEnum;
import cn.need.cloud.upms.client.constant.enums.ReadStateEnum;
import cn.need.cloud.upms.mapper.user.UserAccountMapper;
import cn.need.cloud.upms.model.entity.user.User;
import cn.need.cloud.upms.model.entity.user.UserAccount;
import cn.need.cloud.upms.model.vo.setting.UserAccountInfoVO;
import cn.need.cloud.upms.service.tenant.TenantService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import lombok.NonNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户账号信息 服务实现
 *
 * <AUTHOR>
 */
@Service
public class UserAccountServiceImpl extends SuperServiceImpl<UserAccountMapper, UserAccount> implements UserAccountService {

    @Resource
    @Lazy
    private UserService userService;
    @Resource
    private TenantService tenantService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(@NonNull Long userId, @NonNull String account, @NonNull String accountType) {
        //账号检查
        checkUserAccount(account, userId);
        return super.insert(buildUserAccount(userId, account, accountType));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccount(@NonNull Long userId, @NonNull String account, @NonNull String accountType) {
        //账号检查
        checkUserAccount(account, userId);
        UserAccount userAccount = getByUserId(userId, accountType);
        if (ObjectUtil.isNotNull(userAccount)) {
            userAccount.setAccountType(accountType);
//            userAccount.setUserAccount(account);
            update(userAccount);
        } else {
            insert(userId, account, accountType);
        }
    }

    @Override
    public UserAccount listUserAccountByUserId(@NonNull Long userId) {
        List<UserAccount> accountList = lambdaQuery().eq(UserAccount::getUserId, userId).list();
        return ObjectUtil.isEmpty(accountList) ? new UserAccount() : accountList.get(0);
    }


    @Override
    public UserAccount listUserAccountByUserIdNeOpenId(@NonNull Long userId) {
        List<UserAccount> accountList = lambdaQuery().eq(UserAccount::getUserId, userId).ne(UserAccount::getAccountType, AccountTypeEnum.WX_OPEN_ID.getCode()).list();
        return ObjectUtil.isEmpty(accountList) ? new UserAccount() : accountList.get(0);
    }

    @Override
    public UserAccount loginByAccount(String account) {
        return lambdaQuery().eq(UserAccount::getUserAccount, account).one();
        // return lambdaQuery().eq(UserAccount::getUserAccount, account).eq(UserAccount::getState, ReadState.READ.getState()).one();
    }

    @Override
    public UserAccount getByUsername(String username) {
        return super.lambdaQuery().eq(UserAccount::getUserAccount, username).one();
    }

    @Override
    public UserAccount getByAccountAndType(String account, AccountTypeEnum type) {
        return lambdaQuery()
                .eq(UserAccount::getUserAccount, account)
                .eq(UserAccount::getAccountType, type.getCode())
                .one();
    }

    @Override
    public List<UserAccount> listByAccountsAndType(List<String> accounts, String type) {
        return ObjectUtil.isEmpty(accounts) ? Lists.arrayList() : lambdaQuery().in(UserAccount::getUserAccount, accounts).eq(UserAccount::getAccountType, type).list();
    }

    @Override
    public UserAccount getByUserIdAndType(Long userId, String type) {
        List<UserAccount> accounts = lambdaQuery().eq(UserAccount::getUserId, userId).eq(UserAccount::getAccountType, type).list();
        if (ObjectUtil.isNotEmpty(accounts)) {
            return accounts.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<UserAccount> listByUserIds(List<Long> userIds) {
        return ObjectUtil.isEmpty(userIds) ? Lists.arrayList() : lambdaQuery().in(UserAccount::getUserId, userIds).list();
    }

    @Override
    public List<UserAccount> listByTypeAndIds(String type, List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Lists.arrayList();
        }
        return lambdaQuery().eq(UserAccount::getAccountType, type).in(UserAccount::getUserId, ids).list();
    }

    @Override
    public List<UserAccount> listByUserAccountAndType(List<String> userAccountList, AccountTypeEnum accountType) {
        return lambdaQuery().in(UserAccount::getUserAccount, userAccountList).eq(UserAccount::getAccountType, accountType.getCode()).list();
    }

    @Override
    public UserAccount getByUserId(Long userId) {
        return ObjectUtil.isEmpty(userId) ? new UserAccount() : lambdaQuery().in(UserAccount::getUserId, userId).one();
    }

    @Override
    public UserAccount getByAccountAndUser(String account, Long userId, Long tenantId) {
        return mapper.getByAccountAndUser(account, null, tenantId, userId);
    }

    @Override
    public UserAccountInfoVO userByEmail(String email) {
        // 根据邮箱获取用户账户基本信息
        UserAccount userAccount = getByAccount(StringUtil.trim(email));
        // 检查用户账户是否存在，存在则返回null
        if (ObjectUtil.isEmpty(userAccount)) {
            return null;
        }

        // 根据用户账户中的用户ID获取用户详细信息
        User user = userService.getById(userAccount.getUserId());
        // 检查用户信息是否存在，不存在则抛出异常
        if (ObjectUtil.isEmpty(user)) {
            throw new BusinessException("id: " + userAccount.getUserId() + " not found in user");
        }

        // 将用户信息复制到视图对象中
        UserAccountInfoVO userAccountInfoVO = BeanUtil.copyNew(user, UserAccountInfoVO.class);
        // 设置用户对应的租户列表
        userAccountInfoVO.setTenantList(tenantService.listTenantByUserId(user.getId()));

        // 返回用户账户信息视图对象
        return userAccountInfoVO;
    }

    //************************************************** 私有方法 *******************************************************

    /**
     * 构建用户账号
     *
     * @param userId      用户id
     * @param account     账号名
     * @param accountType 账号类型
     * @return 用户账号对象
     */
    private UserAccount buildUserAccount(Long userId, String account, String accountType) {
        UserAccount userAccount = new UserAccount();
        userAccount.setUserId(userId);
        userAccount.setUserAccount(account);
        userAccount.setAccountType(accountType);
        userAccount.setState(1);
        return userAccount;
    }

    /**
     * 检查账号是否重复
     *
     * @param account 用户账号
     * @param userId  用户id
     */
    private void checkUserAccount(String account, Long userId) {
        //检查账号是否重复
        UserAccount userAccount = getByAccountAndUser(account, userId, null);
        if (ObjectUtil.isNotNull(userAccount)) {
            if (!ObjectUtil.equal(userId, userAccount.getUserId())) {
                throw new BusinessException("该账号已存在!");
            }
        }
    }

    /**
     * 通过账号 查询有效账号
     *
     * @param account 账号
     * @return UserAccount 账号
     */
    private UserAccount getActiveByAccount(String account) {
        return lambdaQuery().eq(UserAccount::getUserAccount, account).eq(UserAccount::getState, ReadStateEnum.READ.getState()).one();
    }

    /**
     * 通过账号 查询有效账号
     *
     * @param account 账号
     * @return UserAccount 账号
     */
    private UserAccount getByAccount(String account) {
        return lambdaQuery().eq(UserAccount::getUserAccount, account).one();
    }

    /**
     * 通过userid查询
     *
     * @param userId      用户id
     * @param accountType 账号类型
     * @return UserAccount
     */
    private UserAccount getByUserId(Long userId, String accountType) {
        return lambdaQuery().eq(UserAccount::getUserId, userId).eq(UserAccount::getAccountType, accountType).one();
    }

}
