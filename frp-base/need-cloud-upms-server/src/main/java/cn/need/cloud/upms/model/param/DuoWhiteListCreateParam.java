package cn.need.cloud.upms.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 二次验证白名单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "二次验证白名单 vo对象")
public class DuoWhiteListCreateParam implements Serializable {


    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ipAddress;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;

    /**
     * 删除备注
     */
    @Schema(description = "删除备注")
    private String deletedNote;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID")
    private Long createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者ID
     */
    @Schema(description = "更新者ID")
    private Long updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志
     */
    @Schema(description = "删除标志")
    private Integer removeFlag;

}