package cn.need.cloud.upms.controller.permissions;

import cn.need.cloud.upms.cache.util.UserCacheUtil;
import cn.need.cloud.upms.converter.permissions.RoleConverter;
import cn.need.cloud.upms.model.entity.permissions.Role;
import cn.need.cloud.upms.model.param.permissions.RoleCreateParam;
import cn.need.cloud.upms.model.param.permissions.RoleUpdateParam;
import cn.need.cloud.upms.model.query.role.RoleQuery;
import cn.need.cloud.upms.model.vo.permissions.RolePageVO;
import cn.need.cloud.upms.model.vo.permissions.RoleSimpleVO;
import cn.need.cloud.upms.model.vo.permissions.RoleVO;
import cn.need.cloud.upms.service.permissions.RoleService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.tenant.util.WebFrameworkUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统角色信息 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/upms/role")
@Tag(name = "系统角色信息")
@Slf4j
@RequiredArgsConstructor
public class RoleController extends AbstractRestController<RoleService, Role, RoleConverter, RoleVO> {

    @Operation(summary = "新增系统角色信息", description = "接收系统角色信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) RoleCreateParam insertParam) {
        // 打印传参信息
        log.info("====> /api/upms/role/insert, insertParam={}", JsonUtil.toJson(insertParam));
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改系统角色信息", description = "接收系统角色信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) RoleUpdateParam updateParam) {
        // 打印传参信息
        log.info("====> /api/upms/role/update, updateParam={}", JsonUtil.toJson(updateParam));
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除系统角色信息", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 打印传参信息
        log.info("====> /api/upms/role/remove, params={}", JsonUtil.toJson(deletedNoteParam));
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id启用禁用系统角色信息", description = "根据数据主键id，从数据库中启用禁用其对应的数据")
    @GetMapping(value = "/active/{id}")
    public Result<Integer> remove(@PathVariable @Parameter(description = "数据主键id", required = true) Long id) {
        // 打印传参信息
        log.info("====> /api/upms/role/active/{}", id);
        // 校验参数
        Validate.notNull(id, "The id value cannot be null.");
        // 执行删除并返回结果
        return super.active(id, null);
    }


    @Operation(summary = "根据id获取系统角色信息详情", description = "根据数据主键id，从数据库中获取其对应的系统角色信息详情")
    @GetMapping(value = "/detail/{id}")
    public Result<RoleVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 打印传参信息
        log.info("====> /api/upms/role/detail/{}", id);
        // 获取系统角色信息详情
        RoleVO detailVo = service.detailById(id);
        // 填充创建人
        UserCacheUtil.filledCreateBy(detailVo);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取系统角色信息分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的系统角色信息列表")
    @PostMapping(value = "/list")
    public Result<PageData<RolePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<RoleQuery> search) {
        // 打印传参信息
        log.info("====> /api/upms/role/list, search={}", JsonUtil.toJson(search));
        // 获取系统角色信息分页
        PageData<RolePageVO> resultPage = service.pageByQuery(search);
        // 填充创建人
        UserCacheUtil.filledCreateBy(resultPage.getRecords());
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "获取系统角色简易信息列表")
    @GetMapping(value = "/list-simple")
    public Result<List<RoleSimpleVO>> listSimple() {
        // 打印传参信息
        log.info("====> /api/upms/role/list-simple");
        // 设置指定的租户
        TenantContextHolder.setTenantId(Users.tenantId());
        // 返回结果
        return success(service.listSimple());
    }

    @Operation(summary = "获取系统角色简易信息列表")
    @GetMapping(value = "/list-ignore-simple")
    public Result<List<RoleSimpleVO>> listIgnoreSimple() {
        // 获取前端传过来的租户
        Long tenantId = WebFrameworkUtils.getTenantId(request);
        // 设置指定的租户
        TenantContextHolder.setTenantId(tenantId);

        // 打印传参信息
        log.info("====> /api/upms/role/list-ignore-simple");
        // 返回结果
        return success(service.listSimple());
    }

    @Operation(summary = "初始化缓存", description = "初始化缓存")
    @PostMapping(value = "/init-cache")
    public Result<Integer> initCache() {
        log.info("====> /api/upms/role/init-cache");
        service.initRoleCache();
        return Result.ok();
    }

}
