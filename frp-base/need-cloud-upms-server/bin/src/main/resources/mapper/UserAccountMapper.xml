<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.user.UserAccountMapper">

    <select id="getByAccountAndUser"
            resultType="cn.need.cloud.upms.model.entity.user.UserAccount">
        select t.*
        from upms_user_account t
        left join upms_user t1 on t.user_id = t1.id and t1.remove_flag = 0
        where t.remove_flag = 0
        <if test="account != null and account != ''">
            and t.user_account = #{account}
        </if>
        <if test="accountType != null and accountType !=''">
            and t.account_type = #{accountType}
        </if>
        <if test="tenantId != null and tenantId !=''">
            and t1.tenant_id = #{tenantId}
        </if>
        <if test="userId != null and userId !=''">
            and t.user_id = #{userId}
        </if>
        limit 1
    </select>
</mapper>