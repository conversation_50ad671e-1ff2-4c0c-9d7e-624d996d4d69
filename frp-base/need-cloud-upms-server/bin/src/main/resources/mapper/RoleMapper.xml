<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.permissions.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.upms.model.entity.permissions.Role">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="version" property="version"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_name" property="roleName"/>
        <result column="role_name_en" property="roleNameEn"/>
        <result column="description" property="description"/>
        <result column="state" property="state"/>
        <result column="role_group" property="roleGroup"/>
        <result column="sorting" property="sorting"/>
        <result column="deleted_note" property="deletedNote"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.version,
        t.role_code,
        t.role_name,
        t.role_name_en,
        t.description,
        t.state,
        t.role_group,
        t.sorting,
        t.deleted_note
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.upms.model.vo.permissions.RolePageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        upms_role t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.roleCode != null and qo.roleCode != ''">
            AND t.role_code = #{qo.roleCode}
        </if>
        <if test="qo.roleCodeList != null and qo.roleCodeList.size > 0 ">
            AND t.role_code in
            <foreach collection="qo.roleCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.roleName != null and qo.roleName != ''">
            AND t.role_name like CONCAT('%',#{qo.roleName},'%')
        </if>
        <if test="qo.roleNameList != null and qo.roleNameList.size > 0 ">
            AND t.role_name in
            <foreach collection="qo.roleNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.roleNameEn != null and qo.roleNameEn != ''">
            AND t.role_name_en like CONCAT('%',#{qo.roleNameEn},'%')
        </if>
        <if test="qo.description != null and qo.description != ''">
            AND t.description = #{qo.description}
        </if>
        <if test="qo.state != null">
            AND t.state = #{qo.state}
        </if>
        <if test="qo.roleGroup != null and qo.roleGroup != ''">
            AND t.role_group = #{qo.roleGroup}
        </if>
        <if test="qo.roleGroupList != null and qo.roleGroupList.size > 0 ">
            AND t.role_group in
            <foreach collection="qo.roleGroupList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.sorting != null">
            AND t.sorting = #{qo.sorting}
        </if>
    </sql>

    <select id="findGroupsByNameFilterForWorkFlow" resultType="cn.need.cloud.upms.model.entity.permissions.Role">
        select r.id,r.role_name from upms_role r
        where r.remove_flag=0
        <!-- 查询条件 -->
        <if test="filter != null and filter != ''">
            AND r.role_name LIKE CONCAT('%',#{filter},'%')
        </if>
    </select>

    <select id="pageByCondition" resultType="cn.need.cloud.upms.model.entity.permissions.Role">
        select * from upms_role where remove_flag = 0
        <if test="roleDTO.tenantId!=null">
            and tenant_id = #{roleDTO.tenantId,jdbcType=BIGINT}
        </if>
        <if test="roleDTO.roleCode!=null and roleDTO.roleCode!='' ">
            and role_code = #{roleDTO.roleCode,jdbcType=VARCHAR}
        </if>
        <if test="roleDTO.roleCodeList!=null and roleDTO.roleCodeList.size()>0 ">
            and role_code in
            <foreach collection="roleDTO.roleCodeList" item="roleCode" open="(" separator="," close=")">
                #{roleCode}
            </foreach>
        </if>
        <if test="roleDTO.roleName!=null and roleDTO.roleName!=''">
            and role_name = #{roleDTO.roleName,jdbcType=VARCHAR}
        </if>
        <if test="roleDTO.state!=null">
            and state = #{roleDTO.state,jdbcType=INTEGER}
        </if>
        <if test="roleDTO.roleGroup!=null and roleDTO.roleGroup!='' ">
            and role_group = #{roleDTO.roleGroup,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
