<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.permissions.PermissionsMapper">

    <select id="listByUserId" resultType="cn.need.cloud.upms.model.entity.permissions.Permissions">
        select p.id,
        p.permissions_code,
        p.permissions_name,
        p.permissions_type,
        p.href,
        p.icon,
        p.icon_type,
        p.parent_id,
        p.depth,
        p.sorting,
        p.state,
        p.remove_flag
        from upms_permissions p
        where p.id in
        (
        select rp.permissions_id
        from upms_role_permissions rp
        LEFT JOIN upms_business_role br on br.role_id = rp.role_id
        where
        rp.remove_flag = 0
        <if test="@com.baomidou.mybatisplus.core.toolkit.ObjectUtils@isNotEmpty(userId)">
            and business_id = #{userId}
            and br.business_type = 'BUSINESS_USER'
        </if>
        and br.remove_flag = 0
        and rp.remove_flag = 0
        )
        <if test="@com.baomidou.mybatisplus.core.toolkit.ObjectUtils@isNotEmpty(terminal)">
            and p.terminal = #{terminal}
        </if>
        <if test="@com.baomidou.mybatisplus.core.toolkit.ObjectUtils@isNotEmpty(type)">
            and p.permissions_type in
            <foreach collection="type" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="listByRoleIds" resultType="cn.need.cloud.upms.model.entity.permissions.Permissions">
        select
        p.id,
        p.permissions_code,
        p.permissions_name,
        p.permissions_type,
        p.href,
        p.icon,
        p.icon_type,
        p.parent_id,
        p.depth,
        p.sorting,
        p.state,
        p.remove_flag
        from upms_permissions p
        where p.id in
        (
        select rp.permissions_id
        from upms_role_permissions rp
        where
        rp.remove_flag = 0
        and rp.role_id in
        <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
        <if test="@com.baomidou.mybatisplus.core.toolkit.ObjectUtils@isNotEmpty(terminal)">
            and p.terminal = #{terminal}
        </if>
        <if test="@com.baomidou.mybatisplus.core.toolkit.ObjectUtils@isNotEmpty(type)">
            and p.permissions_type in
            <foreach collection="type" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
