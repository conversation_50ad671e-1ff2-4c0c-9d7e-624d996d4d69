<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.permissions.BusinessRoleMapper">

    <select id="roleIdsByBusinessIdAndRoleGroups" resultType="java.lang.Long">
        SELECT
        DISTINCT t.role_id
        FROM
        upms_business_role t
        LEFT JOIN upms_role r ON t.role_id = r.id AND r.remove_flag = 0
        WHERE
        t.remove_flag = 0
        AND r.state = 1
        AND t.business_id = #{businessId}
        AND t.business_type = #{businessType}
        AND r.role_group IN
        <foreach collection="roleGroups" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="roleIdsByBusinessIdsAndType" resultType="cn.need.cloud.upms.model.vo.permissions.BusinessAndRoleVO">
        SELECT
        t.* , r.role_group
        FROM
        upms_business_role t
        LEFT JOIN upms_role r ON t.role_id = r.id AND r.remove_flag = 0
        WHERE
        t.remove_flag = 0
        AND r.state = 1
        AND t.business_type = #{businessType}
        AND t.business_id IN
        <foreach collection="businessIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>