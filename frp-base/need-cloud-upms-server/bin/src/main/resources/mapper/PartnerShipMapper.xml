<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.setting.PartnerShipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.upms.model.entity.setting.PartnerShip">
        <result column="id" property="id"/>
        <result column="provider_partner_id" property="providerPartnerId"/>
        <result column="client_partner_id" property="clientPartnerId"/>
        <result column="provider_type" property="providerType"/>
        <result column="active_flag" property="activeFlag"/>
        <result column="note" property="note"/>
        <result column="version" property="version"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.id,
        t.provider_partner_id,
        t.client_partner_id,
        t.provider_type,
        t.active_flag,
        t.note,
        t.version,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.providerPartnerId != null">
            AND t.provider_partner_id = #{qo.providerPartnerId}
        </if>
        <if test="qo.clientPartnerId != null">
            AND t.client_partner_id = #{qo.clientPartnerId}
        </if>
        <if test="qo.providerType != null and qo.providerType != ''">
            AND t.provider_type = #{qo.providerType}
        </if>
        <if test="qo.activeFlag != null">
            AND t.active_flag = #{qo.activeFlag}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note LIKE CONCAT('%', #{qo.note}, '%')
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time >= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time >= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.upms.model.vo.setting.PartnerShipPageVO">
        SELECT
        <include refid="BaseColumnList"/>
        FROM
        partner_ship t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
    </select>

</mapper>
