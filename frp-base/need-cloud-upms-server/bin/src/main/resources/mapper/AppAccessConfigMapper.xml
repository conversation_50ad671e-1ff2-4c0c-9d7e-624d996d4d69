<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.setting.AppAccessConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.upms.model.entity.setting.AppAccessConfig">
        <result column="id" property="id"/>
        <result column="version" property="version"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="app_id" property="appId"/>
        <result column="app_secret" property="appSecret"/>
        <result column="name" property="name"/>
        <result column="service_type" property="serviceType"/>
        <result column="active_flag" property="activeFlag"/>
        <result column="note" property="note"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="default_operator_id" property="defaultOperatorId"/>
        <result column="default_tenant_id" property="defaultTenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.version,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.app_id,
        t.app_secret,
        t.name,
        t.service_type,
        t.active_flag,
        t.note,
        t.deleted_note,
        t.default_operator_id,
        t.default_tenant_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.upms.model.vo.setting.AppAccessConfigPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        app_access_config t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.appId != null and qo.appId != ''">
            AND t.app_id = #{qo.appId}
        </if>
        <if test="qo.appSecret != null and qo.appSecret != ''">
            AND t.app_secret = #{qo.appSecret}
        </if>
        <if test="qo.name != null and qo.name != ''">
            AND t.name = #{qo.name}
        </if>
        <if test="qo.serviceType != null and qo.serviceType != ''">
            AND t.service_type = #{qo.serviceType}
        </if>
        <if test="qo.serviceTypeList != null and qo.serviceTypeList.size > 0 ">
            AND t.service_type in
            <foreach collection="qo.serviceTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.activeFlag != null">
            AND t.active_flag = #{qo.activeFlag}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.defaultOperatorId != null">
            AND t.default_operator_id = #{qo.defaultOperatorId}
        </if>
        <if test="qo.defaultTenantId != null">
            AND t.default_tenant_id = #{qo.defaultTenantId}
        </if>
    </sql>

</mapper>