<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.tenant.TenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="PageVoResultMap" type="cn.need.cloud.upms.model.vo.tenant.TenantPageVO" autoMapping="true">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="state" property="state"/>
        <result column="tenant_logo" property="tenantLogo"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="name" property="name"/>
        <result column="abbr_name" property="abbrName"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="partner_type" property="partnerType"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="business_id" property="businessId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.version,
        t.tenant_code,
        t.state,
        t.tenant_logo,
        t.create_by_name,
        t.update_by_name,
        t.name,
        t.abbr_name,
        t.contact_name,
        t.contact_email,
        t.address_name,
        t.address_company,
        t.address_country,
        t.address_state,
        t.address_city,
        t.address_zip_code,
        t.address_addr1,
        t.address_addr2,
        t.address_addr3,
        t.address_email,
        t.address_phone,
        t.address_note,
        t.partner_type,
        t.deleted_note,
        t.address_is_residential,
        t.default_flag
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultMap="PageVoResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        upms_tenant t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <select id="listTenantByUserId" resultMap="PageVoResultMap">
        SELECT
        <include refid="Base_Column_List"/>,
        ut.active_flag as user_active_flag,
        ut.id AS business_id
        FROM
        upms_user_tenant ut
        LEFT JOIN upms_tenant t ON ut.tenant_id = t.id
        WHERE
        t.remove_flag = 0 and ut.remove_flag = 0 and ut.user_id = #{userId}
    </select>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.tenantCode != null and qo.tenantCode != ''">
            AND t.tenant_code = #{qo.tenantCode}
        </if>
        <if test="qo.state != null">
            AND t.state = #{qo.state}
        </if>
        <if test="qo.name != null and qo.name != ''">
            AND t.name = #{qo.name}
        </if>
        <if test="qo.nameList != null and qo.nameList.size > 0">
            AND t.name in
            <foreach collection="qo.nameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.abbrName != null and qo.abbrName != ''">
            AND t.abbr_name = #{qo.abbrName}
        </if>
        <if test="qo.abbrNameList != null and qo.abbrNameList.size > 0">
            AND t.abbr_name in
            <foreach collection="qo.abbrNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.contactName != null and qo.contactName != ''">
            AND t.contact_name like concat('%',#{qo.contactName},'%')
        </if>
        <if test="qo.contactNameList != null and qo.contactNameList.size > 0">
            AND t.contact_name in
            <foreach collection="qo.contactNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.contactEmail != null and qo.contactEmail != ''">
            AND t.contact_email like concat('%',#{qo.contactEmail},'%')
        </if>
        <if test="qo.contactEmailList != null and qo.contactEmailList.size > 0">
            AND t.contact_email in
            <foreach collection="qo.contactEmailList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.addressName != null and qo.addressName != ''">
            AND t.address_name = #{qo.addressName}
        </if>
        <if test="qo.addressCompany != null and qo.addressCompany != ''">
            AND t.address_company = #{qo.addressCompany}
        </if>
        <if test="qo.addressCountry != null and qo.addressCountry != ''">
            AND t.address_country = #{qo.addressCountry}
        </if>
        <if test="qo.addressState != null and qo.addressState != ''">
            AND t.address_state = #{qo.addressState}
        </if>
        <if test="qo.addressCity != null and qo.addressCity != ''">
            AND t.address_city = #{qo.addressCity}
        </if>
        <if test="qo.addressZipCode != null and qo.addressZipCode != ''">
            AND t.address_zip_code = #{qo.addressZipCode}
        </if>
        <if test="qo.addressAddr1 != null and qo.addressAddr1 != ''">
            AND t.address_addr1 = #{qo.addressAddr1}
        </if>
        <if test="qo.addressAddr2 != null and qo.addressAddr2 != ''">
            AND t.address_addr2 = #{qo.addressAddr2}
        </if>
        <if test="qo.addressAddr3 != null and qo.addressAddr3 != ''">
            AND t.address_addr3 = #{qo.addressAddr3}
        </if>
        <if test="qo.addressEmail != null and qo.addressEmail != ''">
            AND t.address_email = #{qo.addressEmail}
        </if>
        <if test="qo.addressPhone != null and qo.addressPhone != ''">
            AND t.address_phone = #{qo.addressPhone}
        </if>
        <if test="qo.addressNote != null and qo.addressNote != ''">
            AND t.address_note = #{qo.addressNote}
        </if>
        <if test="qo.partnerTypeList != null and qo.partnerTypeList.size > 0 ">
            <foreach collection="qo.partnerTypeList" item="item">
                AND JSON_CONTAINS( t.partner_type, CONCAT('"', #{item}, '"'), '$' )
            </foreach>
        </if>
        <if test="qo.partnerType != null and qo.partnerType != '' ">
            AND JSON_CONTAINS( t.partner_type, CONCAT('"', #{qo.partnerType}, '"'), '$' )
        </if>
    </sql>


</mapper>
