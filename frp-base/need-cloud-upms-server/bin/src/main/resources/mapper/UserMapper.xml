<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.user.UserMapper">


    <select id="findUsersByNameFilterForWorkFlow" resultType="cn.need.cloud.upms.model.entity.user.User">
        select u.id,u.nick_name,u.email from upms_user u
        where u.remove_flag=0
        <!-- 查询条件 -->
        <if test="filter != null and filter != ''">
            AND (u.nick_name LIKE CONCAT('%',#{filter},'%') or u.email LIKE CONCAT('%',#{filter},'%') )
        </if>
    </select>

    <select id="findUsersByGroupForWorkFlow" resultType="cn.need.cloud.upms.model.entity.user.User">
        select u.id, u.nick_name, u.email
        from upms_user u
        where u.remove_flag = 0
          and EXISTS(select business_type
                     from upms_business_role ur
                     where ur.remove_flag = 0
                       and ur.role_id = #{groupId}
                       and ur.business_type = 'BUSINESS_USER'
                       and ur.business_id = u.id)
    </select>

    <select id="listForSelect" resultType="cn.need.cloud.upms.model.vo.UserVO">
        SELECT u.id,u.nick_name,u.email,u.name
        FROM upms_user u
        WHERE u.remove_flag=0
        AND u.user_type!='EXTERNAL' AND u.id NOT IN
        (SELECT b.`business_id`
        FROM `upms_role` AS r
        INNER JOIN `upms_business_role` AS b
        ON r.id=b.`role_id`
        WHERE r.role_code="ADMIN" AND `business_type`="BUSINESS_USER"
        AND b.`remove_flag`=0)
        <!-- 查询条件 -->
        <if test="condition.name!= null and condition.name != ''">
            AND (u.name LIKE CONCAT('%',#{condition.name},'%')
            OR u.nick_name LIKE CONCAT('%',#{condition.name},'%'))
        </if>
        limit 20
    </select>


    <select id="listByRoleCodeForSelect" resultType="cn.need.cloud.upms.model.vo.UserVO">
        SELECT u.id,u.nick_name,u.email,u.name
        FROM upms_user u
        WHERE u.remove_flag=0
        <if test="condition.roleCode!= null and condition.roleCode != ''">
            AND u.id IN(
            SELECT b.`business_id`
            FROM `upms_role` AS r
            INNER JOIN `upms_business_role` AS b
            ON r.id=b.`role_id`
            WHERE r.role_code=#{condition.roleCode}
            AND b.`remove_flag`=0
            )
        </if>
        <!-- 查询条件 -->
        <if test="condition.name!= null and condition.name != ''">
            AND (u.name LIKE CONCAT('%',#{condition.name},'%')
            OR u.nick_name LIKE CONCAT('%',#{condition.name},'%'))
        </if>
    </select>


    <select id="listUserIdByRoleCode" resultType="java.lang.Long">
        SELECT u.id
        FROM upms_user u
        WHERE u.remove_flag = 0
          and u.state = 1

          AND u.id IN (SELECT b.`business_id`
                       FROM `upms_role` AS r
                                INNER JOIN `upms_business_role` AS b
                                           ON r.id = b.`role_id`
                       WHERE r.role_code = #{roleCode}
                         AND b.`remove_flag` = 0)
    </select>

    <select id="listByCondition" resultType="cn.need.cloud.upms.model.vo.UserVO">
        select
        u.id,
        user_type,
        tenant_id,
        u.terminal_type,
        nick_name,
        u.name,
        u.first_name,
        u.last_name,
        u.state,
        u.email,
        u.mobile,
        u.avatar,
        u.third_system,
        u.data_source,
        u.create_by_name,
        u.create_time,
        u.update_by_name,
        u.update_time,
        u.expiring_flag,
        u.expiring_date,
        ac.account_type,
        ac.user_account,
        u.purchasing_group_code,
        u.oaid
        from upms_user as u
        INNER JOIN upms_user_account as ac
        on u.id = ac.user_id and ac.remove_flag=0
        where u.remove_flag=0
        <if test="condition.nickName!=null and condition.nickName.toString()!=''">
            and u.nick_name LIKE CONCAT('%',#{condition.nickName},'%')
        </if>
        <if test="condition.name!=null and condition.name.toString()!=''">
            and u.name LIKE CONCAT('%',#{condition.name},'%')
        </if>
        <if test="condition.userAccount!=null and condition.userAccount.toString()!=''">
            and ac.user_account LIKE CONCAT('%',#{condition.userAccount},'%')
        </if>
        <if test="condition.thirdSystem != null and condition.thirdSystem !=''">
            and u.third_system = #{condition.thirdSystem}
        </if>
        <if test="condition.userType!=null and condition.userType.toString()!=''">
            and u.user_type=#{condition.userType}
        </if>
        <if test="condition.state!=null and condition.state.toString()!=''">
            and u.state=#{condition.state}
        </if>
        <if test="condition.mobile!=null and condition.mobile.toString()!=''">
            and u.mobile LIKE CONCAT('%',#{condition.mobile},'%')
        </if>
        <if test="condition.ids!=null and condition.ids.size()>0">
            and u.id IN
            <foreach collection="condition.ids" item="item" open=" (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.roleName!=null and condition.roleName.toString()!=''">
            and u.id IN (
            SELECT b.`business_id`
            FROM `upms_role` AS r
            INNER JOIN `upms_business_role` AS b
            ON r.id=b.`role_id`
            WHERE r.role_name LIKE CONCAT('%',#{condition.roleName},'%')
            AND b.`remove_flag`=0
            )
        </if>
        <if test="condition.orgNamesStr!=null and condition.orgNamesStr.toString()!=''">
            and u.id IN (
            SELECT b.`user_id`
            FROM `upms_organization` AS r
            INNER JOIN `upms_user_organization` AS b
            ON r.id=b.`org_id`
            WHERE r.org_name LIKE CONCAT('%',#{condition.orgNamesStr},'%')
            AND b.`remove_flag`=0
            )
        </if>
        <if test="condition.tenantName!=null and condition.tenantName!=''">
            and u.tenant_id IN (
            SELECT r.id
            FROM upms_tenant AS r
            WHERE r.tenant_name like concat('%', #{condition.tenantName}, '%')
            AND r.`remove_flag`=0
            )
        </if>
        <if test="condition.enterpriseId!=null">
            AND u.enterprise_id=#{condition.enterpriseId}
        </if>
        <if test="condition.tenantId!=null">
            and u.tenant_id = #{condition.tenantId}
        </if>
        <if test="condition.siteId!=null">
            and u.site_id = #{condition.siteId}
        </if>
        <if test="condition.innerOrOuterType!=null and condition.innerOrOuterType==0">
            and u.user_type != "EXTERNAL"
        </if>
        <if test="condition.innerOrOuterType!=null and condition.innerOrOuterType==1">
            and u.user_type = "EXTERNAL"
        </if>
        <if test="condition.createBy!=null">
            AND u.create_by=#{condition.createBy}
        </if>
        <if test="condition.orgIds!=null and condition.orgIds.size()>0">
            AND EXISTS (select t.user_id from upms_user_organization t where t.remove_flag = 0 and t.user_id = u.id AND
            t.org_id IN
            <foreach collection="condition.orgIds" item="item" open=" (" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="condition.createByName != null and condition.createByName != ''">
            and u.create_by_name LIKE CONCAT('%', #{condition.createByName}, '%')
        </if>
        <if test="condition.updateByName != null and condition.updateByName != ''">
            and u.update_by_name LIKE CONCAT('%', #{condition.updateByName}, '%')
        </if>
        <if test="condition.createTimeList != null and condition.createTimeList.size() == 2">
            and u.create_time between #{condition.createTimeList[0]} and #{condition.createTimeList[1]}
        </if>
        <if test="condition.updateTimeList != null and condition.updateTimeList.size() == 2">
            and u.update_time between #{condition.updateTimeList[0]} and #{condition.updateTimeList[1]}
        </if>
        order by u.update_time desc
    </select>
    <select id="listNameByCondition" resultType="cn.need.cloud.upms.model.vo.UserSelectVO">
        SELECT
        u.id,
        u.`name`
        FROM
        upms_user u
        WHERE
        u.remove_flag = 0
        AND u.state = 1 AND u.user_type in('ADMIN', 'EMPLOYEE')
        <if test="condition.name!=null and condition.name.toString()!=''">
            and u.name LIKE CONCAT('%',#{condition.name},'%')
        </if>
        <if test="condition.thirdSystem != null and condition.thirdSystem !=''">
            and u.third_system = #{condition.thirdSystem}
        </if>
        <if test="condition.ids!=null and condition.ids.size()>0">
            and u.id IN
            <foreach collection="condition.ids" item="item" open=" (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        limit 10
    </select>
    <select id="listUserIdsByUserType" resultType="java.lang.Long">
        SELECT u.id
        FROM upms_user u
        WHERE u.remove_flag = 0
        AND u.state = 1
        <if test="userType!=null and userType.toString()!=''">
            AND u.user_type = #{userType}
        </if>
    </select>
    <select id="selectTenantUser" resultType="cn.need.cloud.upms.model.vo.UserVO">
        SELECT u.id,u.nick_name,u.email,u.name
        FROM upms_user u
        WHERE u.remove_flag=0
        <if test="condition.tenantId !=null ">
            AND u.id not in ( select ur.user_id from upms_user_tenant ur where ur.tenant_id= #{condition.tenantId} and
            ur.remove_flag = 0)
        </if>
        <if test="condition.name!=null and condition.name!=''">
            and u.name LIKE CONCAT('%',#{condition.name},'%')
        </if>
    </select>

    <select id="searchRoleUser" resultType="cn.need.cloud.upms.model.entity.user.User">
        SELECT uu.id,
        uu.`name` as name,
        uu.mobile,
        uu.state
        FROM upms_user uu
        WHERE uu.remove_flag = 0
        <if test="condition.name != null and condition.name != ''">
            AND uu.name LIKE CONCAT('%',#{condition.name},'%')
        </if>
        <if test="condition.mobile != null and condition.mobile != ''">
            AND uu.mobile LIKE CONCAT('%',#{condition.mobile},'%')
        </if>
        <if test="condition.state != null and condition.state != ''">
            AND uu.state = #{condition.state}
        </if>
        <if test="condition.userIds != null and condition.userIds.size() > 0">
            AND uu.id IN
            <foreach collection="condition.userIds" item="item" open=" (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="condition.bindFlag.toString() == 'Y'.toString() and condition.roleId != ''">
                AND uu.id IN (
                SELECT ur.business_id
                FROM upms_business_role ur
                WHERE ur.role_id = #{condition.roleId}
                AND ur.remove_flag = 0
                )
            </when>
            <when test="condition.bindFlag.toString() == 'N'.toString() and condition.roleId != ''">
                AND uu.id NOT IN (
                SELECT ur.business_id
                FROM upms_business_role ur
                WHERE ur.role_id = #{condition.roleId}
                AND ur.remove_flag = 0
                )
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>
    <select id="pageRoleUser" resultType="cn.need.cloud.upms.model.vo.UserVO">
        SELECT uu.id,
        uu.`name` as name,
        uu.mobile,
        uu.state
        FROM upms_user uu
        WHERE uu.remove_flag = 0
        <if test="condition.name != null and condition.name != ''">
            AND uu.name LIKE CONCAT('%',#{condition.name},'%')
        </if>
        <if test="condition.mobile != null and condition.mobile != ''">
            AND uu.mobile LIKE CONCAT('%',#{condition.mobile},'%')
        </if>
        <if test="condition.state != null and condition.state != ''">
            AND uu.state = #{condition.state}
        </if>
        <if test="condition.userIds != null and condition.userIds.size() > 0">
            AND uu.id IN
            <foreach collection="condition.userIds" item="item" open=" (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="condition.bindFlag.toString() == 'Y'.toString() and condition.roleId != ''">
                AND uu.id IN (
                SELECT ur.business_id
                FROM upms_business_role ur
                WHERE ur.role_id = #{condition.roleId}
                AND ur.remove_flag = 0
                )
            </when>
            <when test="condition.bindFlag.toString() == 'N'.toString() and condition.roleId != ''">
                AND uu.id NOT IN (
                SELECT ur.business_id
                FROM upms_business_role ur
                WHERE ur.role_id = #{condition.roleId}
                AND ur.remove_flag = 0
                )
            </when>
            <otherwise>
            </otherwise>
        </choose>
        order by uu.id desc
    </select>

    <!-- 根据用户id查询 -->
    <select id="listDtoByUserId" resultType="cn.need.cloud.upms.client.dto.UserDTO">
        select tu.*,
        tua.user_account
        from upms_user tu
        left join upms_user_account tua on tu.id = tua.user_id and tu.remove_flag = 0 and tua.remove_flag = 0
        where tu.id in
        <foreach collection="userIdList" item="item" open=" (" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listByMobileAndTenantId" resultType="cn.need.cloud.upms.model.entity.user.User">
        select t.*
        from upms_user t
        <if test="tenantId != null and tenantId != ''">
            join upms_user_tenant t1 on t.id = t1.user_id and t1.remove_flag = 0
        </if>
        where t.mobile = #{mobile}
        <if test="tenantId != null and tenantId != ''">
            and t1.tenant_id = #{tenantId}
        </if>
        and t.remove_flag = 0
    </select>
    <select id="listByAccountAndTenantIdAndAccountType" resultType="cn.need.cloud.upms.model.vo.UserVO">
        select t.*,
        t2.user_account,
        t2.account_type
        from upms_user t
        join upms_user_account t2 on t.id = t2.user_id and t2.remove_flag = 0
        <if test="tenantId != null and tenantId != ''">
            join upms_user_tenant t1 on t.id = t1.user_id and t1.remove_flag = 0
        </if>
        where t.remove_flag = 0
        <if test="account != null and account !=''">
            and t2.user_account = #{account}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and t1.tenant_id = #{tenantId}
        </if>
        <if test="accountType != null and accountType !=''">
            and t2.account_type = #{accountType}
        </if>
    </select>

    <select id="listAll" resultType="cn.need.framework.common.dict.entity.UserCache">
        SELECT u.*,
               ua.user_account AS username
        FROM upms_user_account ua
                 LEFT JOIN upms_user u ON ua.user_id = u.id
            AND u.remove_flag = 0
        WHERE ua.remove_flag = 0
          AND ua.account_type = 'USER_NAME'
          AND u.id IS NOT NULL
    </select>

</mapper>
