<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.need.cloud</groupId>
        <artifactId>frp-base</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>
    <artifactId>need-cloud-upms-server</artifactId>
    <packaging>jar</packaging>
    <name>need-cloud-upms-server</name>
    <description>the upms server Center for uneed need-cloud</description>

    <!-- 参数配置 -->
    <properties>

    </properties>

    <dependencies>
        <!-- upms服务client依赖，版本与当前项目保持一致-->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-upms-client</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--用户缓存-->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-upms-cache</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- uneed common 依赖-->
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dict-client</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-tenant</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
