package cn.need.cloud.dict.model.vo;

import cn.need.framework.common.core.tree.Node;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * 行政区域 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "行政区域 VO对象")
public class RegionTreeVO implements Node<String, RegionTreeVO> {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private String id;

    /**
     * 行政区域编号
     */
    @Schema(description = "行政区域编号")
    private String regionCode;

    /**
     * 行政区域名称
     */
    @Schema(description = "行政区域名称")
    private String regionName;


    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private String parentId;

    /**
     * 深度，用来记录树结构的层级关系
     */
    @Schema(description = "深度，用来记录树结构的层级关系")
    private Integer depth;

    /**
     * 路径，用来记录树结构数据id的路径，用','分隔
     */
    @Schema(description = "路径，用来记录树结构数据id的路径，用','分隔")
    private String path;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private Double sorting;

    /**
     * 数据字典子集集合
     */
    @Schema(description = "数据字典子集集合")
    private List<RegionTreeVO> children;

    /**
     * 是否包含子集
     */
    @Schema(description = "是否包含子集")
    private Boolean hasChildren;


    /**
     * 选中状态:
     * checked 已选
     * half 半选择
     * "" 末选
     */
    private String checkStatus;

    @Override
    public CharSequence getName() {
        return this.regionName;
    }

    @Override
    public Comparable<?> getWeight() {
        return this.sorting;
    }

    @Override
    public List<RegionTreeVO> getChildren() {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        return this.children;
    }
}