<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.need.framework</groupId>
        <artifactId>need-parent</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>

    <groupId>cn.need.cloud</groupId>
    <artifactId>frp-base-boot</artifactId>
    <version>frp-dev.41-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>frp-base-boot</name>

    <!-- 参数配置 -->
    <properties>

    </properties>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <!-- alibaba nacos-->
        <!-- 注册中心、配置中心-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <!-- spring cloud -->
        <!-- openfeign 服务调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!--开启监控检查-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- sky walking logback support -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <!-- 微服务引用 -->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dfs-server</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dict-server</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-log-server</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-upms-server</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-tenant</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-mybatis</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-security</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-job</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>frp-base</finalName>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!--配置true，指的是下面的文件会替换文件中的@xxx@占位符，且下面必须配置插件maven-resources-plugin -->
                <filtering>true</filtering>
                <includes>
                    <include>application.yaml</include>
                    <include>application-${ACTIVE_ENV}.yaml</include>
                    <include>application.yml</include>
                    <include>application-${ACTIVE_ENV}.yml</include>
                    <include>bootstrap.yaml</include>
                    <include>bootstrap-${ACTIVE_ENV}.yaml</include>
                    <include>bootstrap.yml</include>
                    <include>bootstrap-${ACTIVE_ENV}.yml</include>
                </includes>
            </resource>
            <!--配置不需要占位符替换的文件，配置了之后才会copy到target中 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>logback-spring.xml</include>
                    <include>META-INF/**</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <propertiesEncoding>UTF-8</propertiesEncoding>
                    <encoding>UTF-8</encoding>
                    <!--设置为true的话，则采用默认占位符@或者$来进行替换，只能做简单替换，并不能支持${xx:1}这种默认的语法,这种语法只支持读取系统或环境变量 -->
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
