version: '3'

networks:
    uneed-net-overlay:
        external: true

services:
    uneed-product:
        image: apache/skywalking-java-agent:9.1.0-java17
        hostname: frp-base
        container_name: frp-base
        restart: always
        privileged: true
        environment:
            - TZ=Asia/Shanghai
            - SW_AGENT_NAME=frp-base
            #- SW_AGENT_COLLECTOR_BACKEND_SERVICES=skywalking-oap:11800
            - JAVA_OPTS=-Xms256m -Xmx512m
        ports:
            - "10010:10010"
        networks:
            - uneed-net-overlay
        volumes:
            - /app/frp-base/frp-base-boot.jar:/app/frp-base-boot.jar
            - /app/frp-base/logs:/skywalking/logs:rw
        command:
            /bin/bash -c "java -Xloggc:/data/logs/ -DNACOS_HOST=nacosServer -DNACOS_PORT=8848 -DFRP_MYSQL_HOST=mysqlNode -DFRP_MYSQL_PORT=3306 -DFRP_MYSQL_DB=frp-base -jar /app/frp-base-boot.jar --info"
        healthcheck:
            test: [ "CMD-SHELL", "curl -sS http://localhost:10110/actuator/health || exit 1" ]
            interval: 1m
            timeout: 10s
            retries: 3
